import os
import yaml
from typing import Dict, List, Optional, Tuple
from collections.abc import Mapping
from config.logger import setup_logging

logger = setup_logging()
TAG = __name__

class RoleLoader:
    """角色配置加载器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化角色加载器
        
        Args:
            config_path: 角色配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 获取项目根目录
            self.project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.default_config_path = None  # 不再使用默认配置文件
            self.custom_config_path = os.path.join(self.project_dir, "data", "roles.yaml")
        else:
            self.default_config_path = config_path
            self.custom_config_path = None
        
        self._roles_config = None
        self._role_aliases_map = None
        
    def _read_config(self, config_path: str) -> Dict:
        """读取单个配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file) or {}
        except FileNotFoundError:
            logger.bind(tag=TAG).error(f"配置文件不存在: {config_path}")
            raise
        except yaml.YAMLError as e:
            logger.bind(tag=TAG).error(f"解析配置文件失败 {config_path}: {e}")
            raise
        except Exception as e:
            logger.bind(tag=TAG).error(f"读取配置文件时发生错误 {config_path}: {e}")
            raise
    
    def _merge_configs(self, default_config: Dict, custom_config: Dict) -> Dict:
        """
        递归合并配置，custom_config优先级更高
        
        Args:
            default_config: 默认配置
            custom_config: 用户自定义配置
            
        Returns:
            合并后的配置
        """
        if not isinstance(default_config, Mapping) or not isinstance(custom_config, Mapping):
            return custom_config
        
        merged = dict(default_config)
        
        for key, value in custom_config.items():
            if (
                key in merged
                and isinstance(merged[key], Mapping)
                and isinstance(value, Mapping)
            ):
                merged[key] = self._merge_configs(merged[key], value)
            else:
                merged[key] = value
        
        return merged
        
    def _load_config(self) -> Dict:
        """加载角色配置文件"""
        if self._roles_config is None:
            try:
                # 确定要加载的配置文件路径
                config_path = self.custom_config_path if self.custom_config_path else self.default_config_path
                
                # 检查配置文件是否存在
                if not os.path.exists(config_path):
                    raise FileNotFoundError(f"找不到角色配置文件: {config_path}")
                
                # 加载配置文件
                self._roles_config = self._read_config(config_path)
                logger.bind(tag=TAG).info(f"成功加载角色配置文件: {config_path}")
                    
                # 验证配置结构
                if not isinstance(self._roles_config, dict):
                    raise ValueError("角色配置文件格式错误：根节点必须是字典")
                    
                if 'roles' not in self._roles_config:
                    logger.bind(tag=TAG).warning("角色配置文件中未找到'roles'节点，将使用空配置")
                    self._roles_config['roles'] = {}
                    
            except Exception as e:
                logger.bind(tag=TAG).error(f"加载角色配置文件时发生错误: {e}")
                raise
                
        return self._roles_config
    
    def _build_aliases_map(self) -> Dict[str, str]:
        """构建角色别名映射表"""
        if self._role_aliases_map is None:
            config = self._load_config()
            self._role_aliases_map = {}
            
            role_aliases = config.get('role_aliases', {})
            for role_name, aliases in role_aliases.items():
                if isinstance(aliases, list):
                    for alias in aliases:
                        self._role_aliases_map[alias.lower()] = role_name
                        
            logger.bind(tag=TAG).debug(f"构建角色别名映射表: {self._role_aliases_map}")
        
        return self._role_aliases_map
    
    def get_available_roles(self) -> List[str]:
        """获取所有可用的角色名称"""
        config = self._load_config()
        return list(config.get('roles', {}).keys())
    
    def get_role_config(self, role_name: str) -> Optional[Dict]:
        """
        获取指定角色的配置
        
        Args:
            role_name: 角色名称或别名
            
        Returns:
            角色配置字典，包含prompt和voice字段；如果角色不存在则返回None
        """
        config = self._load_config()
        roles = config.get('roles', {})
        
        # 首先尝试直接匹配角色名
        if role_name in roles:
            return roles[role_name]
        
        # 然后尝试通过别名匹配
        aliases_map = self._build_aliases_map()
        normalized_name = role_name.lower()
        
        if normalized_name in aliases_map:
            actual_role_name = aliases_map[normalized_name]
            return roles.get(actual_role_name)
        
        logger.bind(tag=TAG).warning(f"未找到角色配置: {role_name}")
        return None
    
    def get_default_role(self) -> str:
        """获取默认角色名称"""
        config = self._load_config()
        return config.get('default_role', '俏皮')
    
    def normalize_role_name(self, role_name: str) -> Optional[str]:
        """
        将角色名称或别名标准化为正式的角色名称
        
        Args:
            role_name: 角色名称或别名
            
        Returns:
            标准化后的角色名称，如果不存在则返回None
        """
        config = self._load_config()
        roles = config.get('roles', {})
        
        # 首先尝试直接匹配角色名
        if role_name in roles:
            return role_name
        
        # 然后尝试通过别名匹配
        aliases_map = self._build_aliases_map()
        normalized_name = role_name.lower()
        
        if normalized_name in aliases_map:
            return aliases_map[normalized_name]
        
        return None
    
    def get_role_aliases(self, role_name: str) -> List[str]:
        """
        获取指定角色的所有别名
        
        Args:
            role_name: 角色名称
            
        Returns:
            别名列表
        """
        config = self._load_config()
        role_aliases = config.get('role_aliases', {})
        return role_aliases.get(role_name, [])
    
    def reload_config(self):
        """重新加载配置文件"""
        self._roles_config = None
        self._role_aliases_map = None
        logger.bind(tag=TAG).info("角色配置已重新加载")


# 全局角色加载器实例
_role_loader = None

def get_role_loader() -> RoleLoader:
    """获取全局角色加载器实例"""
    global _role_loader
    if _role_loader is None:
        _role_loader = RoleLoader()
    return _role_loader 