# YuYan微服务Docker部署指南

## 概述

这个方案将YuYan服务拆分为独立的微服务，通过Docker Compose进行编排，实现服务隔离和故障容错。

## 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│  Nginx          │    │  HTTP API       │
│  (反向代理)      │────│  Service        │
│  Port: 80/443   │    │  Port: 8100     │
└─────────────────┘    └─────────────────┘
         │                       │
         │              ┌─────────────────┐
         └──────────────│  WebSocket      │
                        │  Service        │
                        │  Port: 8000     │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  Redis          │
                        │  (共享存储)      │
                        │  Port: 6379     │
                        └─────────────────┘
```

## 服务组件

### 1. WebSocket服务 (websocket-server)
- **职责**: 处理实时语音对话
- **端口**: 8000 (WebSocket), 8080 (OTA)
- **资源**: 2GB内存, 1CPU
- **健康检查**: Socket连接测试

### 2. HTTP API服务 (http-api-server)
- **职责**: 处理RESTful API请求
- **端口**: 8100
- **资源**: 1GB内存, 0.5CPU
- **健康检查**: /health端点

### 3. Redis服务 (redis)
- **职责**: 用户数据和会话存储
- **端口**: 6379
- **资源**: 512MB内存, 0.25CPU
- **数据持久化**: 启用AOF

### 4. Nginx反向代理 (nginx)
- **职责**: 统一入口和负载均衡
- **端口**: 80, 443
- **功能**: 路由分发、CORS处理、SSL终结

## 快速开始

### 前置要求
- Docker Engine 20.0+
- Docker Compose 2.0+
- 至少4GB可用内存

### 一键启动
```bash
# 克隆项目后，直接启动
./docker-start.sh
```

### 手动启动
```bash
# 1. 构建镜像
docker build -f Dockerfile.websocket -t yuyan-websocket:latest .
docker build -f Dockerfile.http -t yuyan-http-api:latest .

# 2. 启动服务
docker-compose -f docker-compose.microservices.yml up -d

# 3. 检查状态
./docker-manage.sh status
```

## 服务管理

### 基本操作
```bash
# 启动所有服务
./docker-manage.sh start

# 停止所有服务
./docker-manage.sh stop

# 重启服务
./docker-manage.sh restart

# 查看状态
./docker-manage.sh status

# 查看日志
./docker-manage.sh logs                    # 所有服务
./docker-manage.sh logs websocket-server   # 特定服务
```

### 健康检查
```bash
# 检查服务健康状态
./docker-manage.sh health

# 实时监控
./docker-manage.sh monitor
```

### 扩缩容
```bash
# 扩容HTTP API服务到3个实例
./docker-manage.sh scale http-api-server=3

# 扩容WebSocket服务到2个实例
./docker-manage.sh scale websocket-server=2
```

## 服务端点

### 直接访问
- **WebSocket**: `ws://localhost:8000/xiaozhi/v1/`
- **HTTP API**: `http://localhost:8100/api/v1/`
- **OTA服务**: `http://localhost:8080/xiaozhi/ota/`

### 通过Nginx访问
- **统一入口**: `http://localhost/`
- **API接口**: `http://localhost/api/v1/`
- **WebSocket**: `ws://localhost/xiaozhi/v1/`
- **健康检查**: `http://localhost/health`

## 监控和日志

### 健康检查端点
- **WebSocket**: `http://localhost:8000/health`
- **HTTP API**: `http://localhost:8100/health`
- **Nginx状态**: `http://localhost/health`

### 日志查看
```bash
# 查看所有日志
docker-compose -f docker-compose.microservices.yml logs

# 查看特定服务日志
docker-compose -f docker-compose.microservices.yml logs websocket-server

# 实时跟踪日志
docker-compose -f docker-compose.microservices.yml logs -f
```

### 资源监控
```bash
# 查看容器资源使用
docker stats

# 使用管理脚本监控
./docker-manage.sh monitor
```

## 故障排查

### 常见问题

#### 1. 服务启动失败
```bash
# 检查容器状态
docker-compose -f docker-compose.microservices.yml ps

# 查看失败原因
docker-compose -f docker-compose.microservices.yml logs [service-name]

# 重启特定服务
docker-compose -f docker-compose.microservices.yml restart [service-name]
```

#### 2. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep -E "(8000|8100|6379|80)"

# 修改配置文件中的端口映射
vim docker-compose.microservices.yml
```

#### 3. 内存不足
```bash
# 检查系统资源
free -h
docker system df

# 清理Docker资源
./docker-manage.sh clean
```

#### 4. Redis连接失败
```bash
# 检查Redis状态
docker-compose -f docker-compose.microservices.yml exec redis redis-cli ping

# 重启Redis
docker-compose -f docker-compose.microservices.yml restart redis
```

### 日志分析
- **WebSocket服务**: `/app/logs/websocket.log`
- **HTTP API服务**: `/app/logs/http-api.log`
- **Nginx日志**: `./logs/nginx/`

## 生产环境配置

### 安全加固
1. **更改默认密码**
   ```bash
   # 在docker-compose.yml中配置Redis密码
   command: redis-server --requirepass your_password
   ```

2. **SSL/TLS配置**
   ```bash
   # 在nginx配置中添加SSL证书
   # 参考 nginx/conf.d/ssl.conf.example
   ```

3. **防火墙设置**
   ```bash
   # 只开放必要端口
   ufw allow 80/tcp
   ufw allow 443/tcp
   ```

### 性能优化
1. **资源限制调整**
   ```yaml
   # 在docker-compose.yml中调整资源限制
   deploy:
     resources:
       limits:
         memory: 4G
         cpus: '2.0'
   ```

2. **Redis配置优化**
   ```bash
   # 根据业务需求调整Redis配置
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   ```

### 数据备份
```bash
# Redis数据备份
docker-compose -f docker-compose.microservices.yml exec redis redis-cli BGSAVE

# 用户数据备份
tar -czf backup-$(date +%Y%m%d).tar.gz data/ logs/
```

## 迁移指南

### 从单体架构迁移
1. **停止原服务**
   ```bash
   # 停止原来的app.py
   pkill -f "python app.py"
   ```

2. **数据迁移**
   ```bash
   # 如果使用了本地存储，需要迁移数据
   cp -r old_data/ data/
   ```

3. **配置调整**
   ```bash
   # 检查config配置是否兼容
   # 特别注意Redis连接配置
   ```

4. **启动新服务**
   ```bash
   ./docker-start.sh
   ```

### 回滚方案
```bash
# 停止Docker服务
./docker-manage.sh stop

# 启动原单体服务
python app.py
```

## 最佳实践

1. **定期更新**
   ```bash
   # 定期更新服务镜像
   ./docker-manage.sh update
   ```

2. **监控告警**
   - 设置健康检查告警
   - 监控资源使用率
   - 配置日志聚合

3. **备份策略**
   - 定期备份Redis数据
   - 配置文件版本控制
   - 容器镜像标签管理

4. **安全更新**
   - 定期更新基础镜像
   - 扫描安全漏洞
   - 审计访问日志

## 支持和帮助

如果遇到问题，请：
1. 查看日志: `./docker-manage.sh logs`
2. 检查健康状态: `./docker-manage.sh health`
3. 查看资源使用: `./docker-manage.sh status`
4. 参考故障排查章节