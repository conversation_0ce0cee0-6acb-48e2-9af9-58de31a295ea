# 备用部署方案

本目录包含非Docker的部署方案，用于特殊场景下的服务启动。

## 文件说明

### 1. `start_services.py` - Python多进程方案
使用Python的multiprocessing模块分离WebSocket和HTTP API服务。

**适用场景**：
- 本地开发调试
- 不使用Docker的环境
- 资源受限的服务器

**使用方法**：
```bash
python start_services.py
```

**优势**：
- 进程隔离，故障不互相影响
- 自动重启机制
- 原生Python环境，无需Docker

### 2. `app_ws.py` - 独立WebSocket服务
只启动WebSocket服务的独立脚本。

**使用方法**：
```bash
python app_ws.py
```

### 3. `app_http.py` - 独立HTTP API服务
只启动HTTP API服务的独立脚本。

**使用方法**：
```bash
python app_http.py
```

## 推荐部署方案

**首选方案**：Docker Compose微服务（项目根目录）
```bash
./docker-start.sh
```

**备用方案**：Python多进程（本目录）
```bash
python start_services.py
```

**单体方案**：原始启动方式（项目根目录）
```bash
python app.py
```

## 选择指南

| 场景 | 推荐方案 | 原因 |
|------|----------|------|
| 生产环境 | Docker Compose | 容器化、监控完善、故障隔离 |
| 开发环境 | Docker Compose | 环境一致性 |
| 调试开发 | Python多进程 | 直接访问Python进程 |
| 资源受限 | Python多进程 | 无Docker开销 |
| 快速测试 | 单体方案 | 启动最快 |
| 容器环境异常 | Python多进程 | 应急备用 |