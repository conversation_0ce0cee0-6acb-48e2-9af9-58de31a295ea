#!/usr/bin/env python3
"""
服务启动管理器 - 分离WebSocket和HTTP API服务
优势：
1. 进程隔离，一个服务崩溃不影响另一个
2. 自动重启机制
3. 统一的日志和监控
4. 优雅关闭处理
"""

import argparse
import asyncio
import multiprocessing
import os
import signal
import sys
import time
from typing import Dict, Optional
from config.settings import load_config
from config.logger import setup_logging

TAG = __name__
logger = setup_logging()

class ServiceManager:
    def __init__(self, ws_port=None, api_port=None):
        self.config = load_config()
        self.processes: Dict[str, multiprocessing.Process] = {}
        self.should_restart = True
        self.ws_port = ws_port
        self.api_port = api_port
        
    @staticmethod
    def start_websocket_service(ws_port=None):
        """启动WebSocket服务（独立进程）"""
        try:
            # 在子进程中重新初始化logger
            logger = setup_logging()
            logger.bind(tag=TAG).info("启动WebSocket服务进程...")
            from app_ws import main as ws_main
            asyncio.run(ws_main(ws_port=ws_port))
        except Exception as e:
            # 在子进程中重新初始化logger
            logger = setup_logging()
            logger.bind(tag=TAG).error(f"WebSocket服务异常退出: {e}")
            sys.exit(1)
    
    @staticmethod
    def start_http_api_service(api_port=None):
        """启动HTTP API服务（独立进程）"""
        try:
            # 在子进程中重新初始化logger
            logger = setup_logging()
            logger.bind(tag=TAG).info("启动HTTP API服务进程...")
            from app_http import main as http_main
            asyncio.run(http_main(api_port=api_port))
        except Exception as e:
            # 在子进程中重新初始化logger
            logger = setup_logging()
            logger.bind(tag=TAG).error(f"HTTP API服务异常退出: {e}")
            sys.exit(1)
    
    def start_services(self):
        """启动所有服务"""
        # 启动WebSocket服务
        ws_process = multiprocessing.Process(
            target=ServiceManager.start_websocket_service,
            args=(self.ws_port,),
            name="WebSocketServer"
        )
        ws_process.start()
        self.processes["websocket"] = ws_process

        # 启动HTTP API服务
        http_process = multiprocessing.Process(
            target=ServiceManager.start_http_api_service,
            args=(self.api_port,),
            name="HttpApiServer"
        )
        http_process.start()
        self.processes["http_api"] = http_process

        # 显示端口信息
        ws_port_info = f" (端口: {self.ws_port})" if self.ws_port else ""
        api_port_info = f" (端口: {self.api_port})" if self.api_port else ""

        logger.bind(tag=TAG).info(f"已启动服务进程:")
        logger.bind(tag=TAG).info(f"  WebSocket服务 PID: {ws_process.pid}{ws_port_info}")
        logger.bind(tag=TAG).info(f"  HTTP API服务 PID: {http_process.pid}{api_port_info}")
    
    def monitor_services(self):
        """监控服务状态并自动重启"""
        while self.should_restart:
            for service_name, process in list(self.processes.items()):
                if not process.is_alive():
                    logger.bind(tag=TAG).warning(f"{service_name}服务进程已退出 (PID: {process.pid})")
                    
                    if self.should_restart:
                        logger.bind(tag=TAG).info(f"重启{service_name}服务...")
                        
                        # 清理旧进程
                        if process.is_alive():
                            process.terminate()
                            process.join(timeout=5)
                        
                        # 重启服务
                        if service_name == "websocket":
                            new_process = multiprocessing.Process(
                                target=ServiceManager.start_websocket_service,
                                args=(self.ws_port,),
                                name="WebSocketServer"
                            )
                        else:  # http_api
                            new_process = multiprocessing.Process(
                                target=ServiceManager.start_http_api_service,
                                args=(self.api_port,),
                                name="HttpApiServer"
                            )
                        
                        new_process.start()
                        self.processes[service_name] = new_process
                        logger.bind(tag=TAG).info(f"{service_name}服务重启成功 (新PID: {new_process.pid})")
            
            time.sleep(2)  # 监控间隔
    
    def shutdown_services(self):
        """优雅关闭所有服务"""
        logger.bind(tag=TAG).info("收到关闭信号，正在关闭所有服务...")
        self.should_restart = False
        
        for service_name, process in self.processes.items():
            if process.is_alive():
                logger.bind(tag=TAG).info(f"关闭{service_name}服务 (PID: {process.pid})...")
                process.terminate()
                
                # 等待优雅关闭
                process.join(timeout=10)
                
                # 如果还没关闭，强制杀死
                if process.is_alive():
                    logger.bind(tag=TAG).warning(f"强制关闭{service_name}服务...")
                    process.kill()
                    process.join()
        
        logger.bind(tag=TAG).info("所有服务已关闭")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='服务启动管理器')
    parser.add_argument('--ws_port', type=int, help='WebSocket服务端口')
    parser.add_argument('--api_port', type=int, help='HTTP API服务端口')
    args = parser.parse_args()

    manager = ServiceManager(ws_port=args.ws_port, api_port=args.api_port)

    # 信号处理
    def signal_handler(signum, frame):
        manager.shutdown_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
    
    try:
        # 启动服务
        manager.start_services()
        
        # 监控服务状态
        manager.monitor_services()
        
    except KeyboardInterrupt:
        manager.shutdown_services()
    except Exception as e:
        logger.bind(tag=TAG).error(f"服务管理器异常: {e}")
        manager.shutdown_services()
        sys.exit(1)

if __name__ == "__main__":
    # 防止在子进程中重复执行
    multiprocessing.set_start_method('spawn', force=True)
    main()