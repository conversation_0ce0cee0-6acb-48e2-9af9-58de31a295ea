#!/usr/bin/env python3
"""
独立的HTTP API服务器
从原app.py中分离出HTTP API服务部分
"""

import argparse
import asyncio
import signal
import sys
from config.settings import load_config
from core.http_api_server import HttpApiServer
from core.utils.util import get_local_ip
from config.logger import setup_logging
from core.utils.redis_client import RedisClient

TAG = __name__
logger = setup_logging()

async def wait_for_exit() -> None:
    """等待退出信号"""
    loop = asyncio.get_running_loop()
    stop_event = asyncio.Event()

    if sys.platform != "win32":  # Unix / macOS
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, stop_event.set)
        await stop_event.wait()
    else:
        # Windows
        try:
            await asyncio.Future()
        except KeyboardInterrupt:
            pass

def check_redis_server(config):
    """检查Redis服务器连接"""
    redis_client = RedisClient(config)
    redis_client.check_redis_sever()

async def main(api_port=None):
    """HTTP API服务主函数"""
    config = load_config()
    check_redis_server(config)

    # 如果指定了端口，覆盖配置文件中的端口
    if api_port is not None:
        if "server" not in config:
            config["server"] = {}
        config["server"]["http_api_port"] = api_port

    # 创建HTTP API服务器（不传递websocket_server参数）
    http_api_server = HttpApiServer(config, websocket_server=None)
    http_api_task = asyncio.create_task(http_api_server.start())

    # 获取HTTP API配置
    http_api_port = int(config.get("server", {}).get("http_api_port", 8100))
    
    logger.bind(tag=TAG).info(
        "HTTP API接口是\thttp://{}:{}/api/v1/",
        get_local_ip(),
        http_api_port,
    )
    
    logger.bind(tag=TAG).info("HTTP API服务器已启动")

    try:
        await wait_for_exit()
    except asyncio.CancelledError:
        logger.bind(tag=TAG).info("HTTP API服务被取消，清理资源中...")
    finally:
        # 取消HTTP API任务
        http_api_task.cancel()

        try:
            await asyncio.wait_for(
                asyncio.gather(http_api_task, return_exceptions=True),
                timeout=10.0
            )
        except asyncio.TimeoutError:
            logger.bind(tag=TAG).warning("HTTP API任务清理超时")

        logger.bind(tag=TAG).info("HTTP API服务已清理完成")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='HTTP API服务器')
    parser.add_argument('--api_port', type=int, help='HTTP API服务端口')
    args = parser.parse_args()

    asyncio.run(main(api_port=args.api_port))