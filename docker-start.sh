#!/bin/bash
# YuYan微服务Docker Compose启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs/nginx
    mkdir -p data
    mkdir -p config
    
    # 确保日志目录权限正确
    chmod 755 logs logs/nginx
    
    log_info "目录创建完成"
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建WebSocket服务镜像
    log_info "构建WebSocket服务镜像..."
    docker build -f Dockerfile.websocket -t yuyan-websocket:latest .
    
    # 构建HTTP API服务镜像
    log_info "构建HTTP API服务镜像..."
    docker build -f Dockerfile.http -t yuyan-http-api:latest .
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动YuYan微服务..."
    
    # 使用Docker Compose启动服务
    docker-compose -f docker-compose.microservices.yml up -d
    
    log_info "服务启动中，等待健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查服务状态
    check_services_health
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    local all_healthy=true
    
    # 检查Redis
    if docker-compose -f docker-compose.microservices.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        log_info "✓ Redis服务健康"
    else
        log_error "✗ Redis服务不健康"
        all_healthy=false
    fi
    
    # 检查HTTP API服务
    if curl -f http://localhost:8100/health > /dev/null 2>&1; then
        log_info "✓ HTTP API服务健康"
    else
        log_error "✗ HTTP API服务不健康"
        all_healthy=false
    fi
    
    # 检查WebSocket服务 (简单的端口检查)
    if nc -z localhost 8000 > /dev/null 2>&1; then
        log_info "✓ WebSocket服务健康"
    else
        log_error "✗ WebSocket服务不健康"
        all_healthy=false
    fi
    
    if [ "$all_healthy" = true ]; then
        log_info "所有服务健康运行"
        show_service_info
    else
        log_warn "部分服务不健康，请检查日志"
        log_info "查看日志命令："
        echo "  docker-compose -f docker-compose.microservices.yml logs"
    fi
}

# 显示服务信息
show_service_info() {
    echo ""
    log_info "服务访问信息："
    echo -e "${BLUE}WebSocket服务:${NC} ws://localhost:8000/xiaozhi/v1/"
    echo -e "${BLUE}HTTP API服务:${NC} http://localhost:8100/api/v1/"
    echo -e "${BLUE}OTA服务:${NC} http://localhost:8080/xiaozhi/ota/"
    echo -e "${BLUE}Nginx统一入口:${NC} http://localhost/"
    echo ""
    log_info "监控命令："
    echo "  docker-compose -f docker-compose.microservices.yml ps    # 查看服务状态"
    echo "  docker-compose -f docker-compose.microservices.yml logs  # 查看日志"
    echo "  docker-compose -f docker-compose.microservices.yml down  # 停止服务"
}

# 主函数
main() {
    echo -e "${BLUE}YuYan微服务Docker启动脚本${NC}"
    echo "==============================="
    
    check_requirements
    create_directories
    build_images
    start_services
    
    log_info "启动完成!"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi