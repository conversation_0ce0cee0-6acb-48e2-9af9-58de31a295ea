# 音频会议记录 API 文档

## 概述

音频会议记录 API 提供异步的 MP3 音频文件处理服务，支持将音频文件转换为结构化的会议纪要。该 API 支持分片处理和累积摘要功能，适用于长时间会议的分段录音处理。

## 基础信息

- **Base URL**: `http://your-server:8100`
- **API 版本**: v1
- **协议**: HTTP/HTTPS
- **数据格式**: JSON (除文件上传外)

## 认证

所有 API 端点都需要认证，请在请求头中包含以下信息：

```http
client-id: your-client-id
device-id: your-device-id
authorization: Bearer your-token
```

## API 端点

### 1. 上传音频文件生成会议纪要

**POST** `/api/v1/audio_summary`

异步上传 MP3 音频文件并生成会议纪要。支持累积摘要功能，可以将新的音频片段与之前的会议纪要合并。

#### 请求

**Content-Type**: `multipart/form-data`

**表单参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `audio` | File | 是 | MP3 音频文件，最大 200MB |
| `text` | String | 否 | 之前的会议纪要文本，用于累积摘要 |

**示例请求**:

```bash
curl -X POST "http://your-server:8100/api/v1/audio_summary" \
  -H "client-id: your-client-id" \
  -H "device-id: your-device-id" \
  -H "authorization: Bearer your-token" \
  -F "audio=@meeting_part1.mp3" \
  -F "text=之前的会议纪要内容..."
```

#### 响应

**成功响应** (HTTP 200):

```json
{
  "success": true,
  "message": "音频摘要任务已启动",
  "data": {
    "task_id": "uuid-task-id-here",
    "status": "processing",
    "filename": "meeting_part1.mp3",
    "file_size": 1048576,
    "previous_summary_length": 150
  },
  "timestamp": "2024-06-26T17:30:00.000Z"
}
```

**错误响应**:

```json
{
  "success": false,
  "message": "只支持MP3格式的音频文件",
  "timestamp": "2024-06-26T17:30:00.000Z"
}
```

**可能的错误**:

- `400`: 文件格式错误、文件过大、缺少必要参数
- `401`: 认证失败
- `500`: 服务器内部错误

---

### 2. 查询音频摘要任务状态

**GET** `/api/v1/audio_summary/{task_id}`

查询指定任务的处理状态和结果。

#### 请求

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `task_id` | String | 是 | 上传音频时返回的任务ID |

**示例请求**:

```bash
curl -X GET "http://your-server:8100/api/v1/audio_summary/uuid-task-id-here" \
  -H "client-id: your-client-id" \
  -H "device-id: your-device-id" \
  -H "authorization: Bearer your-token"
```

#### 响应

**处理中状态** (HTTP 200):

```json
{
  "success": true,
  "message": "任务状态查询成功",
  "data": {
    "task_id": "uuid-task-id-here",
    "status": "processing",
    "created_at": "2024-06-26T17:30:00.000Z",
    "filename": "meeting_part1.mp3",
    "file_size": 1048576
  },
  "timestamp": "2024-06-26T17:30:45.000Z"
}
```

**完成状态** (HTTP 200):

```json
{
  "success": true,
  "message": "任务状态查询成功",
  "data": {
    "task_id": "uuid-task-id-here",
    "status": "completed",
    "created_at": "2024-06-26T17:30:00.000Z",
    "completed_at": "2024-06-26T17:32:30.000Z",
    "filename": "meeting_part1.mp3",
    "file_size": 1048576,
    "summary": "# 会议纪要\n\n## 基本信息\n- 会议时间：2024年06月26日\n- 会议主题：产品功能讨论\n\n## 主要讨论内容\n1. 讨论了新功能的开发计划\n2. 确定了项目时间节点\n\n## 决议事项\n1. 通过新功能开发方案\n2. 安排下周开始实施\n\n## 行动计划\n1. 技术团队准备开发环境\n2. 产品团队完善需求文档\n\n## 其他事项\n无",
    "summary_length": 180
  },
  "timestamp": "2024-06-26T17:33:00.000Z"
}
```

**失败状态** (HTTP 200):

```json
{
  "success": true,
  "message": "任务状态查询成功",
  "data": {
    "task_id": "uuid-task-id-here",
    "status": "failed",
    "created_at": "2024-06-26T17:30:00.000Z",
    "completed_at": "2024-06-26T17:31:15.000Z",
    "filename": "meeting_part1.mp3",
    "file_size": 1048576,
    "error": "处理音频文件时发生错误: 文件格式无效"
  },
  "timestamp": "2024-06-26T17:33:00.000Z"
}
```

**错误响应**:

```json
{
  "success": false,
  "message": "任务不存在或已过期",
  "timestamp": "2024-06-26T17:33:00.000Z"
}
```

## 数据模型

### 任务状态枚举

| 状态 | 描述 |
|------|------|
| `processing` | 任务正在处理中 |
| `completed` | 任务已完成 |
| `failed` | 任务失败 |

### 会议纪要格式

生成的会议纪要采用 Markdown 格式，包含以下标准结构：

```markdown
# 会议纪要

## 基本信息
- 会议时间：YYYY年MM月DD日
- 会议主题：[从音频中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]
```

## 功能特性

### 1. 异步处理

- API 采用异步处理模式，上传文件后立即返回任务ID
- 支持长时间音频文件处理，避免请求超时
- 客户端可以轮询查询任务状态

### 2. 累积摘要

- 支持分片音频处理，适用于长时间会议
- 新的音频片段会与之前的会议纪要智能合并
- 保持会议纪要的连贯性和完整性

### 3. 文件限制

- **格式**: 仅支持 MP3 格式
- **大小**: 最大 200MB
- **验证**: 包含 MP3 文件头验证

### 4. 数据持久化

- 会议纪要自动保存到服务器文件系统
- 支持任务重启后的状态恢复
- 自动清理 7 天以上的历史文件

## 使用场景

### 场景 1: 单次会议录音

```bash
# 1. 上传音频文件
curl -X POST "http://your-server:8100/api/v1/audio_summary" \
  -H "client-id: client123" \
  -H "authorization: Bearer token123" \
  -F "audio=@meeting.mp3"

# 2. 查询处理结果
curl -X GET "http://your-server:8100/api/v1/audio_summary/task-id-123" \
  -H "client-id: client123" \
  -H "authorization: Bearer token123"
```

### 场景 2: 分片会议录音（累积摘要）

```bash
# 1. 上传第一段录音
curl -X POST "http://your-server:8100/api/v1/audio_summary" \
  -H "client-id: client123" \
  -H "authorization: Bearer token123" \
  -F "audio=@meeting_part1.mp3"

# 2. 获取第一段的会议纪要
# ... (等待处理完成并获取结果)

# 3. 上传第二段录音，包含第一段的纪要
curl -X POST "http://your-server:8100/api/v1/audio_summary" \
  -H "client-id: client123" \
  -H "authorization: Bearer token123" \
  -F "audio=@meeting_part2.mp3" \
  -F "text=<previous_summary.txt"
```

## 错误处理

### HTTP 状态码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 500 | 服务器内部错误 |

### 常见错误

| 错误消息 | 原因 | 解决方案 |
|----------|------|----------|
| "只支持MP3格式的音频文件" | 上传了非MP3文件 | 转换文件格式为MP3 |
| "文件大小超过限制" | 文件超过200MB | 压缩文件或分割为多个片段 |
| "未找到音频文件" | 缺少audio字段 | 确保表单字段名为'audio' |
| "认证失败" | 认证信息错误 | 检查client-id和token |
| "音频处理服务不可用" | 服务器配置问题 | 联系管理员 |
| "任务不存在或已过期" | 任务ID无效或过期 | 重新上传音频文件 |

## 最佳实践

### 1. 轮询策略

```javascript
async function pollTaskStatus(taskId) {
  const maxAttempts = 30; // 最多轮询30次
  const interval = 10000; // 10秒间隔
  
  for (let i = 0; i < maxAttempts; i++) {
    const response = await fetch(`/api/v1/audio_summary/${taskId}`);
    const result = await response.json();
    
    if (result.data.status === 'completed') {
      return result.data.summary;
    } else if (result.data.status === 'failed') {
      throw new Error(result.data.error);
    }
    
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  throw new Error('任务处理超时');
}
```

### 2. 分片处理

- 建议将长音频文件分割为10-15分钟的片段
- 按顺序处理，确保会议纪要的连贯性
- 保存每个阶段的累积结果

### 3. 错误重试

- 网络错误时实施指数退避重试
- 文件上传失败时检查文件格式和大小
- 任务失败时可以重新提交

## 技术实现

### 后端架构

- **异步任务处理**: 使用 AsyncIO 实现非阻塞处理
- **AI引擎**: 集成 Google Gemini API 进行音频分析
- **文件存储**: 临时文件自动清理机制
- **任务管理**: 内存 + 文件系统双重存储

### 性能指标

- **处理速度**: 通常 1 分钟音频需要 30-60 秒处理时间
- **并发支持**: 支持多个任务同时处理
- **文件大小**: 最大支持 200MB MP3 文件
- **准确率**: 基于 AI 模型的语音识别和摘要生成

---

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2024-06-26 | 初始版本，支持基础音频摘要功能 |
| 1.1.0 | 2024-06-26 | 添加累积摘要和文件持久化功能 |

## 联系支持

如有技术问题或功能建议，请联系开发团队。