# HTTP API服务Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV SERVICE_TYPE=http-api

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/logs /app/data

# 设置权限
RUN chmod +x /app/alternatives/app_http.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8100/health || exit 1

# 暴露端口
EXPOSE 8100

# 设置用户
RUN groupadd -r yuyan && useradd -r -g yuyan yuyan
RUN chown -R yuyan:yuyan /app
USER yuyan

# 启动命令
CMD ["python", "alternatives/app_http.py"]