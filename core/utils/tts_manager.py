
import asyncio
import os
import queue
import threading
from concurrent.futures import Future, TimeoutError

from config.logger import setup_logging
from core.handle.reportHandle import enqueue_tts_report
from core.handle.sendAudioHandle import sendAudioMessage

TAG = __name__


class TTSManager:
    def __init__(self, config, tts_providers, loop):
        self.logger = setup_logging()
        self.config = config
        self.tts_providers = tts_providers
        self.loop = loop
        self.clients = {}
        self.lock = threading.Lock()

    def register_client(self, client_id, conn_handler):
        with self.lock:
            if client_id not in self.clients:
                self.logger.bind(tag=TAG).info(f"Registering new client: {client_id}")
                client_data = {
                    "conn_handler": conn_handler,
                    "tts_queue": queue.Queue(),
                    "audio_play_queue": queue.Queue(),
                    "stop_event": threading.Event(),
                    "tts_thread": None,
                    "audio_thread": None,
                }
                self.clients[client_id] = client_data
                self._start_worker_threads(client_id)

    def unregister_client(self, client_id):
        with self.lock:
            if client_id in self.clients:
                self.logger.bind(tag=TAG).info(f"Unregistering client: {client_id}")
                client_data = self.clients[client_id]
                client_data["stop_event"].set()

                # Send poison pills to unblock queues
                client_data["tts_queue"].put(None)
                client_data["audio_play_queue"].put(None)

                # Wait for threads to finish
                if client_data["tts_thread"] and client_data["tts_thread"].is_alive():
                    client_data["tts_thread"].join(timeout=2)
                if (
                    client_data["audio_thread"]
                    and client_data["audio_thread"].is_alive()
                ):
                    client_data["audio_thread"].join(timeout=2)

                del self.clients[client_id]
                self.logger.bind(tag=TAG).info(f"Client {client_id} unregistered.")

    def submit_task(self, client_id, text, text_index, voice_engine, voice):
        with self.lock:
            if client_id in self.clients:
                future = Future()
                task = (future, text, text_index, voice_engine, voice)
                self.clients[client_id]["tts_queue"].put(task)
                return future
            else:
                self.logger.bind(tag=TAG).warning(
                    f"Client {client_id} not registered. Cannot submit TTS task."
                )
                return None

    def _start_worker_threads(self, client_id):
        client_data = self.clients[client_id]
        client_data["tts_thread"] = threading.Thread(
            target=self._tts_worker, args=(client_id,), daemon=True
        )
        client_data["audio_thread"] = threading.Thread(
            target=self._audio_worker, args=(client_id,), daemon=True
        )
        client_data["tts_thread"].start()
        client_data["audio_thread"].start()

    def _tts_worker(self, client_id):
        while True:
            try:
                client_data = self.clients.get(client_id)
                if not client_data or client_data["stop_event"].is_set():
                    break

                task = client_data["tts_queue"].get(timeout=1)
                if task is None:  # Poison pill
                    break

                future, text, text_index, voice_engine, voice = task
                conn_handler = client_data["conn_handler"]

                if conn_handler.client_abort:
                    self.logger.bind(tag=TAG).debug(
                        f"Client {client_id} aborted, discarding TTS task."
                    )
                    continue

                try:
                    tts_provider = self.tts_providers.get(voice_engine)
                    if not tts_provider:
                        raise ValueError(f"TTS provider '{voice_engine}' not found.")

                    # Set the voice in the connection's TTS config if provided
                    if voice:
                        if not hasattr(conn_handler, '_user_tts_config') or conn_handler._user_tts_config is None:
                            conn_handler._user_tts_config = {}
                        conn_handler._user_tts_config['voice'] = voice

                    tts_file = tts_provider.to_tts(
                        text, client_id, conn_handler
                    )
                    if tts_file and os.path.exists(tts_file):
                        audio_format = conn_handler.audio_format
                        if audio_format == "pcm":
                            audio_datas, _ = tts_provider.audio_to_pcm_data(tts_file)
                        else:
                            audio_datas, _ = tts_provider.audio_to_opus_data(tts_file)

                        enqueue_tts_report(conn_handler, text, audio_datas)
                        client_data["audio_play_queue"].put(
                            (audio_datas, text, text_index)
                        )
                        future.set_result((tts_file, text, text_index))

                        if tts_provider.delete_audio_file:
                            os.remove(tts_file)
                    else:
                        raise RuntimeError("TTS file not created or not found.")
                except Exception as e:
                    self.logger.bind(tag=TAG).error(f"TTS task failed: {e}")
                    future.set_exception(e)

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"TTS worker error: {e}")

        self.logger.bind(tag=TAG).info(f"TTS worker for client {client_id} stopped.")

    def _audio_worker(self, client_id):
        while True:
            try:
                client_data = self.clients.get(client_id)
                if not client_data or client_data["stop_event"].is_set():
                    break

                task = client_data["audio_play_queue"].get(timeout=1)
                if task is None:  # Poison pill
                    break

                audio_datas, text, text_index = task
                conn_handler = client_data["conn_handler"]

                if conn_handler.client_abort:
                    self.logger.bind(tag=TAG).debug(
                        f"Client {client_id} aborted, discarding audio play task."
                    )
                    continue

                future = asyncio.run_coroutine_threadsafe(
                    sendAudioMessage(conn_handler, audio_datas, text, text_index),
                    self.loop,
                )
                future.result()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.bind(tag=TAG).error(f"Audio worker error: {e}")

        self.logger.bind(tag=TAG).info(
            f"Audio worker for client {client_id} stopped."
        )
