#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CDN文件上传工具
基于 fex-cloud-api.apusai.com 服务
"""

import hashlib
import time
import random
import string
import requests
import os
from typing import Dict, Any, Optional
from config.logger import setup_logging

TAG = "CDNUploader"


class CDNUploader:
    """CDN文件上传器"""
    
    def __init__(self, app_id: str = "f4a73d4e", app_key: str = "ec76c7c23b25a80f8bb07536d6594c88"):
        """
        初始化CDN上传器
        
        Args:
            app_id: 应用ID
            app_key: 应用密钥
        """
        self.app_id = app_id
        self.app_key = app_key
        self.base_url = "https://fex-cloud-api.apusai.com/storage/image"
        self.logger = setup_logging()
    
    def generate_random_alpha_num(self, length: int) -> str:
        """生成随机字母数字字符串"""
        chars = string.ascii_lowercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def generate_signature(self, key: str) -> str:
        """
        生成签名
        
        Args:
            key: 应用密钥
            
        Returns:
            格式化的签名字符串
        """
        now = int(time.time() * 1000)  # 毫秒时间戳
        salt = self.generate_random_alpha_num(6)
        
        # 生成MD5签名
        signature_string = f"{now}{key}{salt}"
        signature = hashlib.md5(signature_string.encode()).hexdigest()
        
        return f"{signature},{now},{salt}"
    
    def upload_file(self, file_path: str, remote_path: str = None) -> Dict[str, Any]:
        """
        上传文件到CDN
        
        Args:
            file_path: 本地文件路径
            remote_path: 远程文件路径，如果不指定则使用默认路径
            
        Returns:
            上传结果字典
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": f"文件不存在: {file_path}"
                }
            
            # 获取文件名
            filename = os.path.basename(file_path)
            
            # 如果没有指定远程路径，使用默认路径
            if not remote_path:
                timestamp = int(time.time())
                remote_path = f"avatars/{timestamp}/{filename}"
            
            # 生成签名
            signature = self.generate_signature(self.app_key)
            
            # 准备表单数据
            with open(file_path, 'rb') as f:
                files = {
                    'file': (filename, f, 'image/*')
                }
                data = {
                    'path': remote_path
                }
                headers = {
                    'X-LC-Id': self.app_id,
                    'X-LC-Sign': signature
                }
                
                self.logger.bind(tag=TAG).info(f"开始上传文件: {file_path} -> {remote_path}")
                
                # 发起上传请求
                response = requests.post(
                    self.base_url,
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=30
                )
                
                response.raise_for_status()
                result = response.json()
                
                self.logger.bind(tag=TAG).info(f"文件上传成功: {result}")
                
                return {
                    "success": True,
                    "data": result,
                    "url": result.get("url", ""),
                    "path": remote_path
                }
                
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"上传文件失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    def upload_bytes(self, file_bytes: bytes, filename: str, remote_path: str = None) -> Dict[str, Any]:
        """
        上传字节数据到CDN
        
        Args:
            file_bytes: 文件字节数据
            filename: 文件名
            remote_path: 远程文件路径
            
        Returns:
            上传结果字典
        """
        try:
            # 如果没有指定远程路径，使用默认路径
            if not remote_path:
                timestamp = int(time.time())
                remote_path = f"avatars/{timestamp}/{filename}"
            
            # 生成签名
            signature = self.generate_signature(self.app_key)
            
            # 准备表单数据
            files = {
                'file': (filename, file_bytes, 'image/*')
            }
            data = {
                'path': remote_path
            }
            headers = {
                'X-LC-Id': self.app_id,
                'X-LC-Sign': signature
            }
            
            self.logger.bind(tag=TAG).info(f"开始上传字节数据: {filename} ({len(file_bytes)} bytes) -> {remote_path}")
            
            # 发起上传请求
            response = requests.post(
                self.base_url,
                files=files,
                data=data,
                headers=headers,
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            self.logger.bind(tag=TAG).info(f"字节数据上传成功: {result}")
            
            return {
                "success": True,
                "data": result,
                "url": result.get("url", ""),
                "path": remote_path
            }
            
        except requests.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"上传字节数据失败: {str(e)}"
            self.logger.bind(tag=TAG).error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }