#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import asyncio
import json
from typing import Dict, Any, Optional, Tuple
from config.logger import setup_logging

TAG = "PolicyCheck"

class PolicyCheck:
    """内容安全检测类，用于检测用户问题和LLM回答是否违规"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.ask_endpoint = "/api/risk-detect/v1/text/ask"
        self.answer_endpoint = "/api/risk-detect/v1/text/answer"
        self.img_endpoint = "/api/risk-detect/v1/img"
        self.logger = setup_logging()
        
        # 默认配置
        self.base_url = "https://ai-guardian-api.apusai.com"
        self.api_key = None
        self.scene = "dazi"
        self.timeout = 10  # 图片检测建议超时时长为10s
        self.enabled = True
        
        # 从配置中获取设置
        if config and 'policy_check' in config:
            policy_config = config['policy_check']
            self.enabled = policy_config.get('enabled', True)
            self.base_url = policy_config.get('base_url', self.base_url)
            self.api_key = policy_config.get('api_key', self.api_key)
            self.timeout = policy_config.get('timeout', self.timeout)
            self.scene = policy_config.get('scene', self.scene)
        
        if not self.api_key and self.enabled:
            self.logger.bind(tag=TAG).warning("Policy check enabled but no API key configured")
            self.enabled = False
            
    def check_question(self, content: str, user_id: str = "123456") -> Dict[str, Any]:
        """
        检测用户提问内容是否违规
        
        Args:
            content: 用户提问内容
            user_id: 用户ID
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "rewrite_content": None,
                "error": None,
                "raw_response": None
            }
        url = self.base_url + self.ask_endpoint
        payload = {
            "content": content,
            "userId": user_id,
            "scene": self.scene
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 打印请求头信息
            self.logger.bind(tag=TAG).info(f"问题检测请求头: {headers}")
            
            # 直接使用同步请求
            response = requests.post(
                url, 
                json=payload, 
                timeout=self.timeout,
                headers=headers
            )
            
            # 打印响应头信息
            self.logger.bind(tag=TAG).info(f"问题检测响应头: {dict(response.headers)}")
            
            result = response.json()
            return self._parse_result(result, "question", question=content, answer=None)
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"问题检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"问题检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    def check_answer(self, question: str, answer: str) -> Dict[str, Any]:
        """
        检测LLM回答内容是否违规
        
        Args:
            question: 用户问题
            answer: LLM回答
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "rewrite_content": None,
                "error": None,
                "raw_response": None
            }
        url = self.base_url + self.answer_endpoint
        payload = {
            "question": question,
            "answer": answer,
            "scene": self.scene
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 打印请求头信息
            self.logger.bind(tag=TAG).info(f"回答检测请求头: {headers}")
            
            # 直接使用同步请求
            response = requests.post(
                url, 
                json=payload, 
                timeout=self.timeout,
                headers=headers
            )
            
            # 打印响应头信息
            self.logger.bind(tag=TAG).info(f"回答检测响应头: {dict(response.headers)}")
            
            result = response.json()
            return self._parse_result(result, "answer", question=question, answer=answer)
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"回答检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"回答检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    def _parse_result(self, result: Dict[str, Any], check_type: str, question: str = None, answer: str = None) -> Dict[str, Any]:
        """
        解析API返回结果

        Args:
            result: API返回的原始结果
            check_type: 检测类型（question/answer）
            question: 用户问题
            answer: LLM回答（在question检测时为None）

        Returns:
            标准化的检测结果
        """
        parsed_result = {
            "is_blocked": False,
            "detect_result": None,
            "label_code": None,
            "rewrite_content": None,
            "error": None,
            "raw_response": result
        }
        
        if 'error' in result:
            parsed_result["error"] = result['error']
            return parsed_result
            
        if 'data' in result:
            data = result['data']
            detect_result = data.get('detectResult', 'PASS')
            parsed_result["detect_result"] = detect_result
            
            # 如果检测结果不是PASS，则认为被拦截
            if detect_result != 'PASS':
                parsed_result["is_blocked"] = True
                parsed_result["label_code"] = data.get('labelCode')
                parsed_result["rewrite_content"] = data.get('rewriteContent')

                # 构建详细的拦截日志信息
                log_msg = (
                    f"{check_type}内容被拦截 - "
                    f"检测结果: {detect_result}, "
                    f"违规标签: {parsed_result['label_code']}"
                )

                if check_type == "question" and question:
                    log_msg += f", 问题内容: {question[:200]}{'...' if len(question) > 200 else ''}"
                elif check_type == "answer" and question and answer:
                    log_msg += (
                        f", 问题: {question[:100]}{'...' if len(question) > 100 else ''}, "
                        f"回答: {answer[:200]}{'...' if len(answer) > 200 else ''}"
                    )

                self.logger.bind(tag=TAG).warning(log_msg)
            else:
                # 构建通过检测的日志信息
                log_msg = f"{check_type}内容检测通过"

                if check_type == "question" and question:
                    log_msg += f" - 问题内容: {question[:100]}{'...' if len(question) > 100 else ''}"
                elif check_type == "answer" and question and answer:
                    log_msg += (
                        f" - 问题: {question[:50]}{'...' if len(question) > 50 else ''}, "
                        f"回答: {answer[:100]}{'...' if len(answer) > 100 else ''}"
                    )

                self.logger.bind(tag=TAG).debug(log_msg)
        
        return parsed_result
    
    def generate_redirect_response_stream(self, llm_provider, session_id: str, blocked_content: str = None):
        """
        生成引导性回复的流式版本，当内容被拦截时使用
        
        Args:
            llm_provider: LLM提供者实例
            session_id: 会话ID
            blocked_content: 被拦截的内容（可选）
            
        Yields:
            流式生成的引导性回复文本块
        """
        try:
            # 构建引导性提示词
            redirect_prompt = (
                "你是一个贴心的闺蜜助手。用户刚才的话题可能不太适合聊，"
                "请你用温柔、友善的语气告诉用户咱们换个话题聊吧，"
                "比如一种说法是：'哎呀，咱们闺蜜之间聊天，换个话题好不好？'你想想其它类似说法。"
                "然后自然地引导到一个轻松愉快的新话题上，"
                "比如聊聊美食、电影、音乐、生活趣事等。"
                "请直接回复内容，不要加任何前缀。"
            )
            
            # 构建对话历史
            dialogue = [
                {"role": "user", "content": redirect_prompt}
            ]
            
            # 调用LLM流式生成引导性回复
            self.logger.bind(tag=TAG).info("开始流式生成引导性回复")
            
            try:
                # 尝试使用流式接口
                for response_chunk in llm_provider.response(session_id, dialogue):
                    # 处理不同的响应格式
                    if isinstance(response_chunk, (tuple, list)) and len(response_chunk) == 2:
                        content, _ = response_chunk  # 忽略tool_calls
                    elif isinstance(response_chunk, str):
                        content = response_chunk
                    elif hasattr(response_chunk, 'content'):
                        content = response_chunk.content
                    else:
                        content = str(response_chunk) if response_chunk is not None else ""
                    
                    if content and len(content) > 0:
                        yield content
                        
            except Exception as stream_error:
                self.logger.bind(tag=TAG).warning(f"流式生成引导性回复失败，使用备用方案: {stream_error}")
                # 如果流式生成失败，直接返回默认引导语
                default_responses = [
                    "哎呀，咱们闺蜜之间聊天，",
                    "换个话题好不好？",
                    "我们来聊聊其他有趣的事情吧！"
                ]
                for chunk in default_responses:
                    yield chunk
                    
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"生成引导性回复异常: {str(e)}")
            # 返回默认引导语的流式版本
            default_responses = [
                "哎呀，咱们闺蜜之间聊天，",
                "换个话题好不好？",
                "你最近有什么好玩的事情吗？"
            ]
            for chunk in default_responses:
                yield chunk
    
    def check_user_avatar(self, img_url: str, user_id: str = "123456") -> Dict[str, Any]:
        """
        检测用户头像是否违规
        
        Args:
            img_url: 图片URL地址
            user_id: 用户ID
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "error": None,
                "raw_response": None
            }
        
        url = self.base_url + self.img_endpoint
        payload = {
            "img": img_url,
            "userId": user_id,
            "channel": "input",  # 用户输入
            "country": "zh",     # 国内
            "scene": "daziUser"  # 用户头像场景
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 打印请求头信息
            self.logger.bind(tag=TAG).info(f"头像检测请求头: {headers}")
            
            response = requests.post(
                url, 
                json=payload, 
                timeout=self.timeout,
                headers=headers
            )
            
            # 打印响应头信息
            self.logger.bind(tag=TAG).info(f"头像检测响应头: {dict(response.headers)}")
            
            result = response.json()
            return self._parse_image_result(result, img_url)
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"头像检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"头像检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    def check_user_nickname(self, nickname: str, user_id: str = "123456") -> Dict[str, Any]:
        """
        检测用户昵称是否违规
        
        Args:
            nickname: 用户昵称
            user_id: 用户ID
            
        Returns:
            检测结果字典，包含是否违规、违规原因等信息
        """
        # 如果未启用检测，直接返回通过
        if not self.enabled:
            return {
                "is_blocked": False,
                "detect_result": "PASS",
                "label_code": None,
                "rewrite_content": None,
                "error": None,
                "raw_response": None
            }
        
        url = self.base_url + self.ask_endpoint
        payload = {
            "content": nickname,
            "userId": user_id,
            "scene": "daziUser"  # 用户昵称场景
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.api_key
        }
        
        try:
            # 打印请求头信息
            self.logger.bind(tag=TAG).info(f"昵称检测请求头: {headers}")
            
            response = requests.post(
                url, 
                json=payload, 
                timeout=self.timeout,
                headers=headers
            )
            
            # 打印响应头信息
            self.logger.bind(tag=TAG).info(f"昵称检测响应头: {dict(response.headers)}")
            
            result = response.json()
            return self._parse_result(result, "nickname", question=nickname, answer=None)
            
        except requests.RequestException as e:
            self.logger.bind(tag=TAG).error(f"昵称检测API请求失败: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"请求失败: {str(e)}",
                "raw_response": None
            }
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"昵称检测异常: {str(e)}")
            return {
                "is_blocked": False,
                "error": f"检测异常: {str(e)}",
                "raw_response": None
            }
    
    def _parse_image_result(self, result: Dict[str, Any], img_url: str) -> Dict[str, Any]:
        """
        解析图片检测API返回结果
        
        Args:
            result: API返回的原始结果
            img_url: 图片URL
            
        Returns:
            标准化的检测结果
        """
        parsed_result = {
            "is_blocked": False,
            "detect_result": None,
            "label_code": None,
            "error": None,
            "raw_response": result
        }
        
        if 'error' in result:
            parsed_result["error"] = result['error']
            return parsed_result
            
        if 'data' in result:
            data = result['data']
            detect_result = data.get('detectResult', 'PASS')
            parsed_result["detect_result"] = detect_result
            
            # 如果检测结果不是PASS，则认为被拦截
            if detect_result != 'PASS':
                parsed_result["is_blocked"] = True
                parsed_result["label_code"] = data.get('labelCode')
                
                # 构建详细的拦截日志信息
                log_msg = (
                    f"头像图片被拦截 - "
                    f"检测结果: {detect_result}, "
                    f"违规标签: {parsed_result['label_code']}, "
                    f"图片URL: {img_url}"
                )
                self.logger.bind(tag=TAG).warning(log_msg)
            else:
                # 构建通过检测的日志信息
                log_msg = f"头像图片检测通过 - 图片URL: {img_url}"
                self.logger.bind(tag=TAG).debug(log_msg)
        
        return parsed_result
