"""MCP服务管理器"""

import asyncio
import os, json
from typing import Dict, Any, List
from .MCPClient import MC<PERSON><PERSON>
from plugins_func.register import register_function, ToolType
from config.config_loader import get_project_dir

TAG = __name__


class MCPManager:
    """管理多个MCP服务的集中管理器"""

    def __init__(self, conn) -> None:
        """
        初始化MCP管理器
        """
        self.conn = conn
        self.config_path = get_project_dir() + "data/.mcp_server_settings.json"
        if os.path.exists(self.config_path) == False:
            self.config_path = ""
            self.conn.logger.bind(tag=TAG).warning(
                f"请检查mcp服务配置文件：data/.mcp_server_settings.json"
            )
        self.client: Dict[str, MCPClient] = {}
        self.tools = []

    def load_config(self) -> Dict[str, Any]:
        """加载MCP服务配置
        Returns:
            Dict[str, Any]: 服务配置字典
        """
        if len(self.config_path) == 0:
            return {}

        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config.get("mcpServers", {})
        except Exception as e:
            self.conn.logger.bind(tag=TAG).error(
                f"Error loading MCP config from {self.config_path}: {e}"
            )
            return {}

    async def initialize_servers(self) -> None:
        """初始化所有MCP服务"""
        config = self.load_config()
        if not config:
            self.conn.logger.bind(tag=TAG).warning("No MCP server configurations found")
            return

        self.conn.logger.bind(tag=TAG).info(f"Initializing {len(config)} MCP servers...")
        
        initialized_count = 0
        for name, srv_config in config.items():
            # 检查是否被禁用
            if srv_config.get("disabled", False):
                self.conn.logger.bind(tag=TAG).warning(f"Skipping disabled MCP server: {name}")
                continue
                
            if not srv_config.get("command") and not srv_config.get("url"):
                self.conn.logger.bind(tag=TAG).warning(
                    f"Skipping server {name}: neither command nor url specified"
                )
                continue

            try:
                self.conn.logger.bind(tag=TAG).info(f"Initializing MCP client: {name}")
                
                # Log the type of connection we're attempting
                if "command" in srv_config:
                    self.conn.logger.bind(tag=TAG).debug(
                        f"Starting process with command: {srv_config['command']} "
                        f"args: {srv_config.get('args', [])}"
                    )
                else:
                    self.conn.logger.bind(tag=TAG).debug(
                        f"Connecting to URL: {srv_config['url']}"
                    )

                client = MCPClient(srv_config)
                # 为每个客户端设置超时
                await asyncio.wait_for(client.initialize(), timeout=30)  # 30秒超时
                self.client[name] = client
                
                # Log successful initialization
                client_tools = client.get_available_tools()
                self.tools.extend(client_tools)
                self.conn.logger.bind(tag=TAG).info(
                    f"Successfully initialized MCP client '{name}' with {len(client_tools)} tools"
                )
                
                # Register each tool
                for tool in client_tools:
                    try:
                        func_name = "mcp_" + tool["function"]["name"]
                        register_function(func_name, tool, ToolType.MCP_CLIENT)(self.execute_tool)
                        self.conn.func_handler.function_registry.register_function(func_name)
                        # self.conn.logger.bind(tag=TAG).debug(
                        #     f"Registered MCP tool: {func_name}"
                        # )
                    except Exception as e:
                        self.conn.logger.bind(tag=TAG).error(
                            f"Failed to register MCP tool {tool.get('name', 'unknown')}: {e}",
                            exc_info=True
                        )
                
                initialized_count += 1

            except asyncio.TimeoutError:
                self.conn.logger.bind(tag=TAG).error(f"Timeout initializing MCP server '{name}'")
                # Don't re-raise, continue with other servers
            except Exception as e:
                self.conn.logger.bind(tag=TAG).error(
                    f"Failed to initialize MCP server '{name}': {e}",
                    exc_info=True
                )
                
                # 添加更详细的诊断信息
                if "Failed to initialize server session" in str(e):
                    self.conn.logger.bind(tag=TAG).error(
                        f"MCP服务 '{name}' 会话初始化失败。这通常表示："
                        f"\n1. MCP服务进程启动失败"
                        f"\n2. 环境变量配置错误"  
                        f"\n3. 网络连接问题"
                        f"\n4. API密钥无效"
                        f"\n配置详情: {config.get(name, {})}"
                    )
                elif "RuntimeError" in str(type(e)):
                    self.conn.logger.bind(tag=TAG).error(
                        f"MCP服务 '{name}' 运行时错误，可能是FastMCP版本兼容性问题"
                    )
                
                # Don't re-raise, continue with other servers
                
        # Only update function descriptions if we have any tools
        if self.tools:
            try:
                self.conn.func_handler.upload_functions_desc()
                self.conn.logger.info(
                    f"Successfully initialized {initialized_count}/{len(config)} MCP servers "
                    f"with {len(self.tools)} total tools"
                )
            except Exception as e:
                self.conn.logger.bind(tag=TAG).error(
                    f"Failed to upload function descriptions: {e}",
                    exc_info=True
                )
        else:
            self.conn.logger.bind(tag=TAG).warning(
                "No MCP tools were registered. Check server configurations and logs for errors."
            )

    def get_all_tools(self) -> List[Dict[str, Any]]:
        """获取所有服务的工具function定义
        Returns:
            List[Dict[str, Any]]: 所有工具的function定义列表
        """
        return self.tools

    def is_mcp_tool(self, tool_name: str) -> bool:
        """检查是否是MCP工具
        Args:
            tool_name: 工具名称
        Returns:
            bool: 是否是MCP工具
        """
        for tool in self.tools:
            if (
                tool.get("function") != None
                and tool["function"].get("name") == tool_name
            ):
                return True
        return False

    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """执行工具调用
        Args:
            tool_name: 工具名称
            arguments: 工具参数
        Returns:
            Any: 工具执行结果
        Raises:
            ValueError: 工具未找到时抛出
        """
        self.conn.logger.bind(tag=TAG).info(
            f"Executing tool {tool_name} with arguments: {arguments}"
        )
        for client in self.client.values():
            if client.has_tool(tool_name):
                return await client.call_tool(tool_name, arguments)

        raise ValueError(f"Tool {tool_name} not found in any MCP server")

    async def cleanup_all(self) -> None:
        """依次关闭所有 MCPClient，不让异常阻断整体流程。"""
        if not self.client:
            self.conn.logger.bind(tag=TAG).info("No MCP clients to cleanup")
            return

        self.conn.logger.bind(tag=TAG).info(f"Cleaning up {len(self.client)} MCP clients...")

        # 创建清理任务列表
        cleanup_tasks = []
        for name, client in list(self.client.items()):
            task = asyncio.create_task(self._cleanup_single_client(name, client))
            cleanup_tasks.append(task)

        # 并发执行清理，但有超时保护
        if cleanup_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*cleanup_tasks, return_exceptions=True),
                    timeout=30  # 总超时时间30秒
                )
            except asyncio.TimeoutError:
                self.conn.logger.bind(tag=TAG).warning("MCP cleanup timed out, forcing cleanup")
                # 取消所有未完成的任务
                for task in cleanup_tasks:
                    if not task.done():
                        task.cancel()

        # 清空客户端字典
        self.client.clear()
        self.tools.clear()
        self.conn.logger.bind(tag=TAG).info("All MCP clients cleanup completed")

    async def _cleanup_single_client(self, name: str, client) -> None:
        """清理单个MCP客户端"""
        try:
            await asyncio.wait_for(client.cleanup(), timeout=10)  # 单个客户端10秒超时
            self.conn.logger.bind(tag=TAG).info(f"MCP client closed: {name}")
        except asyncio.TimeoutError:
            self.conn.logger.bind(tag=TAG).error(f"Timeout closing MCP client {name}, forcing cleanup")
            # 超时时尝试强制清理
            await self._force_kill_client_processes(name, client)
        except Exception as e:
            self.conn.logger.bind(tag=TAG).error(f"Error closing MCP client {name}: {e}")
            # 出错时也尝试强制清理
            await self._force_kill_client_processes(name, client)

    async def _force_kill_client_processes(self, name: str, client) -> None:
        """强制终止MCP客户端相关的进程"""
        try:
            if hasattr(client, 'client') and client.client:
                fastmcp_client = client.client
                if hasattr(fastmcp_client, '_transports'):
                    for transport_name, transport in fastmcp_client._transports.items():
                        if hasattr(transport, '_process') and transport._process:
                            try:
                                process = transport._process
                                if process.poll() is None:  # 进程仍在运行
                                    self.conn.logger.bind(tag=TAG).warning(f"强制终止MCP客户端 {name} 的进程 PID:{process.pid}")
                                    process.terminate()
                                    try:
                                        await asyncio.wait_for(process.wait(), timeout=3.0)
                                    except asyncio.TimeoutError:
                                        self.conn.logger.bind(tag=TAG).warning(f"进程 {process.pid} 未响应terminate，执行kill")
                                        process.kill()
                                        await process.wait()
                                    self.conn.logger.bind(tag=TAG).info(f"进程 {process.pid} 已终止")
                            except Exception as proc_e:
                                self.conn.logger.bind(tag=TAG).error(f"终止进程时出错: {proc_e}")
        except Exception as e:
            self.conn.logger.bind(tag=TAG).error(f"强制清理客户端 {name} 时出错: {e}")
