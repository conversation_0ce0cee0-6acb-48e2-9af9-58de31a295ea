from __future__ import annotations

import asyncio
import weakref
from typing import Optional, List, Dict, Any

# Assuming 'Client' is the main client class from fastmcp
# and 'Tool' is the model for tool representation if specific typing is needed.
# from fastmcp.models import Tool # Uncomment if Tool model is available and needed
from fastmcp import Client as FastMCPClient
from config.logger import setup_logging

TAG = __name__



class MCPClient:
    def __init__(self, config: Dict[str, Any]):
        self.logger = setup_logging()
        self.original_config: Dict[str, Any] = config
        # _transform_config may raise ValueError for unsupported configurations (e.g., stdio)
        self.fastmcp_config: Dict[str, Any] = self._transform_config(config)

        self.client: Optional[FastMCPClient] = None
        self.tools: List[Any] = []  # Will store tool objects from FastMCPClient
        self._context_manager = None  # 存储async context manager
        self._is_initialized = False  # 初始化状态标志
        self._initialization_task = None  # 跟踪初始化任务
        self._context_entered = False  # 跟踪上下文管理器状态

        # Determine a service identifier for logging purposes.
        # Assumes the transformed config will have one primary service entry.
        mcp_servers = self.fastmcp_config.get("mcpServers", {})
        if mcp_servers:
            self._service_id_for_logging: str = next(iter(mcp_servers))
        else:
            # This case should ideally not be reached if _transform_config raises errors for invalid/unsupported configs.
            self._service_id_for_logging: str = "unknown_service"

    def _transform_config(self, original_config: Dict[str, Any]) -> Dict[str, Any]:
        if "url" in original_config:
            # Use 'default_mcp_service' or allow service_id to be specified in original_config
            service_id = original_config.get("service_id", "default_mcp_service")

            # 构建服务器配置
            server_config = {
                "url": original_config["url"],
                # Default to 'sse' transport if not specified, or use provided value
                "transport": original_config.get("transport", "sse")
            }

            # 如果原始配置中有headers，则添加到服务器配置中
            if "headers" in original_config:
                server_config["headers"] = original_config["headers"]
                self.logger.bind(tag=TAG).debug(f"MCP配置: {original_config['headers']}")

            return {
                "mcpServers": {
                    service_id: server_config
                }
            }
        elif "command" in original_config:
            # 支持 stdio transport 配置
            service_id = original_config.get("service_id", "default_mcp_service")
            
            # 构建服务器配置，使用 stdio transport
            # 通过shell启动并重定向stderr到stdout，以捕获可能在stderr上的初始化消息
            command_parts = [original_config["command"]] + original_config.get("args", [])
            full_command = " ".join(command_parts)

            server_config = {
                "command": "/bin/sh",
                "args": ["-c", f"{full_command} 2>&1"],
                "transport": "stdio"
            }
            
            # 添加环境变量
            if "env" in original_config:
                server_config["env"] = original_config["env"]
            
            return {
                "mcpServers": {
                    service_id: server_config
                }
            }
        else:
            raise ValueError(
                "MCPClient config must include either 'url' for SSE/HTTP-based transport "
                "or 'command' for stdio transport."
            )

    async def initialize(self):
        # 检查是否已经初始化
        if self._is_initialized and self.client:
            # 安全检查连接状态
            is_connected = self._is_connected()
            if is_connected:
                self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' already initialized.")
                return

        try:
            # 记录当前任务，确保后续清理在同一任务中进行
            self._initialization_task = asyncio.current_task()
            
            # 【调试日志】记录详细的初始化步骤
            self.logger.bind(tag=TAG).info(f"开始初始化MCP客户端 '{self._service_id_for_logging}'")
            self.logger.bind(tag=TAG).debug(f"初始化任务ID: {id(self._initialization_task)}")
            self.logger.bind(tag=TAG).debug(f"原始配置: {self.original_config}")
            self.logger.bind(tag=TAG).debug(f"转换后配置: {self.fastmcp_config}")
            
            # 预检查配置的有效性
            self.logger.bind(tag=TAG).debug(f"验证MCP配置...")
            try:
                await self._validate_config()
            except Exception as validate_e:
                self.logger.bind(tag=TAG).warning(f"配置验证失败但继续初始化: {validate_e}")
            
            # 创建新的FastMCP客户端
            self.logger.bind(tag=TAG).debug(f"创建FastMCPClient实例...")
            try:
                self.client = FastMCPClient(self.fastmcp_config)
                self.logger.bind(tag=TAG).debug(f"FastMCPClient实例创建成功")
            except Exception as client_e:
                self.logger.bind(tag=TAG).error(f"FastMCPClient创建失败: {client_e}")
                self.logger.bind(tag=TAG).error(f"Client创建错误类型: {type(client_e).__name__}")
                self.logger.bind(tag=TAG).error(f"Client创建错误详情: {str(client_e)}", exc_info=True)
                raise

            # 直接使用FastMCPClient作为context manager
            self.logger.bind(tag=TAG).debug(f"进入context manager...")
            self._context_manager = self.client
            try:
                await self._context_manager.__aenter__()
                self._context_entered = True
                self.logger.bind(tag=TAG).debug(f"Context manager进入成功")
            except Exception as ctx_e:
                self.logger.bind(tag=TAG).error(f"Context manager进入失败: {ctx_e}")
                self.logger.bind(tag=TAG).error(f"Context错误类型: {type(ctx_e).__name__}")
                self.logger.bind(tag=TAG).error(f"Context错误详情: {str(ctx_e)}", exc_info=True)
                # 检查是否是特定的初始化问题
                if "Failed to initialize server session" in str(ctx_e):
                    self.logger.bind(tag=TAG).error(f"检测到服务器会话初始化失败，这通常表示MCP服务端未正常启动或配置错误")
                raise

            # 检查连接状态
            self.logger.bind(tag=TAG).debug(f"检查连接状态...")
            is_connected = self._is_connected()
            self.logger.bind(tag=TAG).debug(f"连接状态: {is_connected}")
            
            if not is_connected:
                self.logger.bind(tag=TAG).error(f"MCPClient failed to connect to service '{self._service_id_for_logging}'.")
                await self._safe_cleanup()
                raise ConnectionError(f"Failed to connect to MCP server '{self._service_id_for_logging}'.")

            # 获取工具列表
            self.logger.bind(tag=TAG).debug(f"获取工具列表...")
            raw_tools = await self.client.list_tools()
            self.tools = raw_tools if raw_tools else []
            self.logger.bind(tag=TAG).debug(f"获取到 {len(self.tools)} 个工具")

            self._is_initialized = True
            self.logger.bind(tag=TAG).info(
                f"Connected via FastMCP to service '{self._service_id_for_logging}', tools = {[t.name for t in self.tools]}"
            )

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error during MCPClient initialization for service '{self._service_id_for_logging}': {e}")
            self.logger.bind(tag=TAG).error(f"异常类型: {type(e).__name__}")
            self.logger.bind(tag=TAG).error(f"异常详情: {str(e)}", exc_info=True)
            await self._safe_cleanup()
            raise  # Re-raise the original initialization exception

    async def cleanup(self):
        """清理MCP客户端连接"""
        if not self._is_initialized:
            self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' already cleaned up or not initialized.")
            return

        self.logger.bind(tag=TAG).info(f"Cleaning up MCPClient for service '{self._service_id_for_logging}'...")
        await self._safe_cleanup()

    async def _safe_cleanup(self):
        """安全清理方法，处理各种异常情况"""
        current_task = asyncio.current_task()
        self.logger.bind(tag=TAG).debug(f"清理任务ID: {id(current_task)}, 初始化任务ID: {id(self._initialization_task) if self._initialization_task else None}")
        
        try:
            if self._context_manager and self._context_entered:
                # 检查是否在同一个任务中
                if current_task == self._initialization_task:
                    self.logger.bind(tag=TAG).debug(f"在同一任务中进行context manager清理")
                    try:
                        await self._context_manager.__aexit__(None, None, None)
                        self._context_entered = False
                        self.logger.bind(tag=TAG).debug(f"MCPClient context manager正常退出 '{self._service_id_for_logging}'")
                    except Exception as e:
                        self.logger.bind(tag=TAG).warning(f"Context manager退出时出错: {e}")
                        # 检查是否是anyio cancel scope错误
                        if "cancel scope" in str(e).lower() or "different task" in str(e).lower():
                            self.logger.bind(tag=TAG).warning(f"检测到cancel scope跨任务错误，强制清理")
                            await self._force_cleanup()
                        else:
                            raise
                else:
                    self.logger.bind(tag=TAG).warning(f"检测到跨任务清理，避免anyio cancel scope错误")
                    await self._force_cleanup()
            elif self.client:
                # 如果没有context manager但有client，尝试直接关闭
                await self._force_cleanup()

        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error during MCPClient cleanup for '{self._service_id_for_logging}': {e}")
            # 最后的强制清理
            await self._force_cleanup()
        finally:
            # 重置所有状态
            self._reset_state()
            self.logger.bind(tag=TAG).info(f"MCPClient for service '{self._service_id_for_logging}' cleaned up.")

    async def _force_cleanup(self):
        """强制清理，不依赖上下文管理器"""
        try:
            if self.client:
                # 尝试各种清理方法
                if hasattr(self.client, 'close'):
                    try:
                        await self.client.close()
                        self.logger.bind(tag=TAG).debug(f"客户端close()调用成功")
                    except Exception as e:
                        self.logger.bind(tag=TAG).debug(f"客户端close()失败: {e}")
                
                # 尝试终止底层进程（对stdio transport很重要）
                if hasattr(self.client, '_transports'):
                    for transport in self.client._transports.values():
                        if hasattr(transport, '_process') and transport._process:
                            try:
                                transport._process.terminate()
                                # 给进程一些时间优雅退出
                                try:
                                    await asyncio.wait_for(transport._process.wait(), timeout=2.0)
                                except asyncio.TimeoutError:
                                    transport._process.kill()
                                self.logger.bind(tag=TAG).debug(f"底层进程已终止")
                            except Exception as e:
                                self.logger.bind(tag=TAG).debug(f"终止进程失败: {e}")
        except Exception as e:
            self.logger.bind(tag=TAG).warning(f"强制清理失败: {e}")

    def _reset_state(self):
        """重置所有内部状态"""
        self.client = None
        self.tools = []
        self._context_manager = None
        self._is_initialized = False
        self._initialization_task = None
        self._context_entered = False

    async def _validate_config(self):
        """验证MCP配置的有效性"""
        try:
            mcp_servers = self.fastmcp_config.get("mcpServers", {})
            if not mcp_servers:
                raise ValueError("无效的MCP配置：未找到mcpServers")
            
            for service_id, server_config in mcp_servers.items():
                self.logger.bind(tag=TAG).debug(f"验证服务 '{service_id}' 的配置...")
                
                if "url" in server_config:
                    # SSE/HTTP传输方式
                    url = server_config["url"]
                    transport = server_config.get("transport", "sse")
                    self.logger.bind(tag=TAG).debug(f"URL配置: {url}, 传输方式: {transport}")
                    
                    # 基本URL格式验证
                    if not url.startswith(("http://", "https://")):
                        raise ValueError(f"无效的URL格式: {url}")
                    
                    # 可以添加更多URL可达性检查，但为了避免阻塞，这里暂时跳过
                    self.logger.bind(tag=TAG).debug(f"URL格式验证通过: {url}")
                    
                elif "command" in server_config:
                    # stdio传输方式
                    command = server_config["command"]
                    args = server_config.get("args", [])
                    self.logger.bind(tag=TAG).debug(f"命令配置: {command}, 参数: {args}")
                    
                    # 简单的命令存在性检查
                    import shutil
                    if isinstance(command, list):
                        cmd_path = command[0] if command else ""
                    else:
                        cmd_path = command
                    
                    if cmd_path and not shutil.which(cmd_path):
                        self.logger.bind(tag=TAG).warning(f"命令可能不存在: {cmd_path}")
                        # 不抛出异常，因为某些命令可能在特殊路径下
                    
                    self.logger.bind(tag=TAG).debug(f"命令配置验证完成: {command}")
                    
                else:
                    raise ValueError(f"服务 '{service_id}' 缺少url或command配置")
                    
            self.logger.bind(tag=TAG).debug(f"MCP配置验证通过")
            
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"MCP配置验证失败: {e}")
            raise


    def _is_connected(self) -> bool:
        """安全检查连接状态"""
        self.logger.bind(tag=TAG).debug(f"检查连接状态详情...")
        self.logger.bind(tag=TAG).debug(f"client存在: {self.client is not None}")
        self.logger.bind(tag=TAG).debug(f"_is_initialized: {self._is_initialized}")
        
        if not self.client:
            self.logger.bind(tag=TAG).debug(f"client不存在，返回False")
            return False
            
        # 注意：在初始化过程中_is_initialized还是False，所以不能依赖它
        try:
            if hasattr(self.client, 'is_connected'):
                self.logger.bind(tag=TAG).debug(f"client有is_connected方法")
                if callable(self.client.is_connected):
                    result = self.client.is_connected()
                    self.logger.bind(tag=TAG).debug(f"is_connected()返回: {result}")
                    return result
                else:
                    result = self.client.is_connected
                    self.logger.bind(tag=TAG).debug(f"is_connected属性值: {result}")
                    return result
            else:
                self.logger.bind(tag=TAG).debug(f"client没有is_connected方法/属性")
                # 检查其他可能的连接状态指示器
                if hasattr(self.client, '_transport'):
                    self.logger.bind(tag=TAG).debug(f"检查transport状态")
                    transport = self.client._transport
                    if transport:
                        self.logger.bind(tag=TAG).debug(f"transport存在: {type(transport)}")
                        # 对于SSE transport，可能有不同的状态检查方法
                        return True  # 假设transport存在就是连接的
                    else:
                        self.logger.bind(tag=TAG).debug(f"transport不存在")
                        return False
                        
        except Exception as e:
            self.logger.bind(tag=TAG).debug(f"检查连接状态时异常: {e}")
            return False
            
        # 如果没有明确的连接状态方法，在context manager成功进入后假设已连接
        self.logger.bind(tag=TAG).debug(f"没有明确的连接状态检查方法，假设已连接")
        return True

    def has_tool(self, name: str) -> bool:
        if not self._is_connected():
            return False
        # 如果传入的名称有mcp_前缀，移除前缀再检查
        if name.startswith('mcp_'):
            original_name = name[4:]  # 移除'mcp_'前缀
            return any(tool.name == original_name for tool in self.tools)
        else:
            return any(tool.name == name for tool in self.tools)

    def get_available_tools(self) -> List[Dict[str, Any]]:
        if not self._is_connected():
            return []
        
        tool_list = []
        for t in self.tools:
            original_name = getattr(t, 'name', 'Unknown Tool')
            # 添加mcp_前缀以匹配注册时的函数名
            prefixed_name = f"mcp_{original_name}"
            tool_info = {
                "type": "function",
                "function": {
                    "name": prefixed_name,
                    "description": getattr(t, 'description', ''),
                    "parameters": getattr(t, 'input_schema', {}),
                },
            }
            tool_list.append(tool_info)
        return tool_list

    async def call_tool(self, name: str, args: dict) -> Any:
        if not self._is_connected():
            self.logger.bind(tag=TAG).error(f"MCPClient for service '{self._service_id_for_logging}' not initialized or connected.")
            raise RuntimeError(f"MCPClient for service '{self._service_id_for_logging}' not initialized or connected.")

        try:
            # 如果传入的名称有mcp_前缀，移除前缀再调用实际工具
            actual_tool_name = name
            if name.startswith('mcp_'):
                actual_tool_name = name[4:]  # 移除'mcp_'前缀
                self.logger.bind(tag=TAG).debug(f"移除MCP前缀: {name} -> {actual_tool_name}")
            
            # Call the tool with positional arguments as expected by FastMCPClient
            result = await self.client.call_tool(actual_tool_name, args)
            # Wrap the result in the expected format with content attribute
            from dataclasses import dataclass
            
            @dataclass
            class TextContent:
                type: str = "text"
                text: str = ""
                annotations: Any = None
                
            # If result is a list, join it with newlines
            if isinstance(result, list):
                text_content = "\n".join(str(item) for item in result)
            else:
                text_content = str(result)
                
            return type('ToolResult', (), {
                'content': [TextContent(text=text_content)],
                'isError': False
            })
        except Exception as e:
            self.logger.bind(tag=TAG).error(f"Error calling tool '{name}' on service '{self._service_id_for_logging}' with args {args}: {e}")
            raise # Re-raise the error, allowing callers to handle it
