#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Policy Check 单元测试脚本
测试头像图片和昵称的违规检测功能
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

from core.policy_check import PolicyCheck
from config.config_loader import load_config, read_config


class PolicyCheckTester:
    """Policy Check 测试器"""
    
    def __init__(self, config_path: str = None):
        """初始化测试器"""
        # 加载配置
        if config_path:
            try:
                # 尝试直接读取指定的配置文件
                if config_path.endswith('.json'):
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        self.config = json.load(f)
                elif config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    self.config = read_config(config_path)
                else:
                    print(f"⚠️  警告: 不支持的配置文件格式: {config_path}")
                    self.config = {}
            except Exception as e:
                print(f"⚠️  警告: 读取配置文件失败: {e}")
                self.config = {}
        else:
            try:
                # 使用默认配置加载器
                self.config = load_config()
            except Exception as e:
                print(f"⚠️  警告: 加载默认配置失败: {e}，将使用默认设置（可能无法连接API）")
                self.config = {}
        
        # 初始化PolicyCheck
        self.policy_check = PolicyCheck(self.config)
        
        # 测试数据路径
        self.test_dir = current_dir
        self.nicknames_file = self.test_dir / "nick_names.csv"
        self.avatars_file = self.test_dir / "avatars.csv"
        self.avatars_dir = self.test_dir / "avatars"
        
        # 测试结果
        self.results = {
            "nickname_tests": [],
            "avatar_tests": [],
            "summary": {}
        }
        
        print(f"📋 测试配置:")
        print(f"   - API启用状态: {self.policy_check.enabled}")
        print(f"   - API地址: {self.policy_check.base_url}")
        print(f"   - API密钥: {'已配置' if self.policy_check.api_key else '未配置'}")
        print(f"   - 超时时间: {self.policy_check.timeout}秒")
        print(f"   - 场景: {self.policy_check.scene}")
        print(f"   - 昵称测试文件: {self.nicknames_file}")
        print(f"   - 头像测试文件: {self.avatars_file}")
        print(f"   - 头像测试目录: {self.avatars_dir}")
        print("-" * 60)

    def load_nicknames(self) -> List[Dict[str, str]]:
        """加载昵称测试数据"""
        nicknames = []
        
        if not self.nicknames_file.exists():
            print(f"❌ 昵称测试文件不存在: {self.nicknames_file}")
            return nicknames
            
        try:
            import csv
            with open(self.nicknames_file, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                for row_num, row in enumerate(csv_reader, 1):
                    if len(row) < 2:
                        print(f"⚠️  第{row_num}行格式错误，跳过: {row}")
                        continue
                    
                    nickname = row[0].strip()
                    expected = row[1].strip().upper()
                    
                    if not nickname:
                        print(f"⚠️  第{row_num}行昵称为空，跳过")
                        continue
                        
                    if expected not in ['BLOCK', 'PASS']:
                        print(f"⚠️  第{row_num}行预期结果无效({expected})，跳过: {nickname}")
                        continue
                    
                    nicknames.append({
                        'nickname': nickname,
                        'expected': expected,
                        'row_num': row_num
                    })
            
            print(f"📝 加载了 {len(nicknames)} 个昵称测试用例")
            return nicknames
            
        except Exception as e:
            print(f"❌ 读取昵称文件失败: {e}")
            return nicknames

    def load_avatar_files(self) -> List[Dict[str, str]]:
        """加载头像测试数据"""
        avatar_files = []
        
        if not self.avatars_file.exists():
            print(f"❌ 头像测试文件不存在: {self.avatars_file}")
            return avatar_files
            
        try:
            import csv
            with open(self.avatars_file, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                for row_num, row in enumerate(csv_reader, 1):
                    if len(row) < 2:
                        print(f"⚠️  第{row_num}行格式错误，跳过: {row}")
                        continue
                    
                    file_path = row[0].strip()
                    expected = row[1].strip().upper()
                    
                    if not file_path:
                        print(f"⚠️  第{row_num}行文件路径为空，跳过")
                        continue
                        
                    if expected not in ['BLOCK', 'PASS']:
                        print(f"⚠️  第{row_num}行预期结果无效({expected})，跳过: {file_path}")
                        continue
                    
                    # 转换为绝对路径
                    if not file_path.startswith('/'):
                        full_path = self.test_dir / file_path
                    else:
                        full_path = Path(file_path)
                    
                    if not full_path.exists():
                        print(f"⚠️  第{row_num}行文件不存在，跳过: {full_path}")
                        continue
                    
                    avatar_files.append({
                        'file_path': str(full_path),
                        'filename': full_path.name,
                        'expected': expected,
                        'row_num': row_num
                    })
            
            print(f"🖼️  加载了 {len(avatar_files)} 个头像测试用例")
            return avatar_files
            
        except Exception as e:
            print(f"❌ 读取头像文件失败: {e}")
            return avatar_files

    def upload_image_to_cdn(self, image_path: str) -> Dict[str, Any]:
        """
        上传图片到CDN服务获取URL
        """
        try:
            # 导入CDN上传器
            from core.utils.cdn_uploader import CDNUploader
            
            uploader = CDNUploader()
            filename = Path(image_path).name
            
            # 使用测试路径
            remote_path = f"test/avatars/{int(time.time())}_{filename}"
            
            result = uploader.upload_file(image_path, remote_path)
            
            if result.get("success", False):
                return {
                    "success": True,
                    "url": result.get("url", ""),
                    "path": result.get("path", "")
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "上传失败")
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"上传异常: {str(e)}"
            }

    def test_nickname(self, nickname_data: Dict[str, str], test_id: int) -> Dict[str, Any]:
        """测试单个昵称"""
        nickname = nickname_data['nickname']
        expected = nickname_data['expected']
        
        print(f"🔍 测试昵称 #{test_id}: {nickname} (预期: {expected})")
        
        start_time = time.time()
        try:
            result = self.policy_check.check_user_nickname(nickname, f"test_user_{test_id}")
            end_time = time.time()
            
            actual_blocked = result.get("is_blocked", False)
            expected_blocked = (expected == "BLOCK")
            
            # 判断是否符合预期
            is_correct = (actual_blocked == expected_blocked)
            
            test_result = {
                "test_id": test_id,
                "nickname": nickname,
                "expected": expected,
                "success": True,
                "response_time": round(end_time - start_time, 3),
                "is_blocked": actual_blocked,
                "expected_blocked": expected_blocked,
                "is_correct": is_correct,
                "detect_result": result.get("detect_result"),
                "label_code": result.get("label_code"),
                "rewrite_content": result.get("rewrite_content"),
                "error": result.get("error"),
                "timestamp": datetime.now().isoformat()
            }
            
            # 输出结果
            if actual_blocked:
                status = "🚫 被拦截"
                details = f" (违规类型: {test_result['label_code']}, 检测结果: {test_result['detect_result']})"
                if test_result.get("rewrite_content"):
                    details += f" (建议修改: {test_result['rewrite_content']})"
            else:
                status = "✅ 通过"
                details = ""
            
            # 预期符合性检查
            if is_correct:
                expectation_status = "✅ 符合预期"
            else:
                expectation_status = "❌ 不符合预期"
                if expected_blocked:
                    expectation_status += " (应被拦截但未拦截)"
                else:
                    expectation_status += " (应通过但被拦截)"
            
            print(f"   结果: {status}{details}")
            print(f"   预期: {expectation_status} (耗时: {test_result['response_time']}s)")
            
            return test_result
            
        except Exception as e:
            end_time = time.time()
            test_result = {
                "test_id": test_id,
                "nickname": nickname,
                "expected": expected,
                "success": False,
                "response_time": round(end_time - start_time, 3),
                "is_correct": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"   结果: ❌ 测试失败 - {e} (耗时: {test_result['response_time']}s)")
            return test_result

    def test_avatar(self, avatar_data: Dict[str, str], test_id: int) -> Dict[str, Any]:
        """测试单个头像"""
        file_path = avatar_data['file_path']
        filename = avatar_data['filename']
        expected = avatar_data['expected']
        
        print(f"🔍 测试头像 #{test_id}: {filename} (预期: {expected})")
        
        start_time = time.time()
        
        # 先上传图片到CDN
        print(f"   📤 上传图片到CDN...")
        upload_result = self.upload_image_to_cdn(file_path)
        
        if not upload_result.get("success", False):
            end_time = time.time()
            test_result = {
                "test_id": test_id,
                "image_file": filename,
                "file_path": file_path,
                "expected": expected,
                "success": False,
                "response_time": round(end_time - start_time, 3),
                "is_correct": False,
                "error": f"CDN上传失败: {upload_result.get('error', '未知错误')}",
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"   结果: ❌ CDN上传失败 - {test_result['error']} (耗时: {test_result['response_time']}s)")
            return test_result
        
        img_url = upload_result.get("url", "")
        print(f"   📤 CDN上传成功: {img_url}")
        
        try:
            # 进行违规检测
            result = self.policy_check.check_user_avatar(img_url, f"test_user_{test_id}")
            end_time = time.time()
            
            actual_blocked = result.get("is_blocked", False)
            expected_blocked = (expected == "BLOCK")
            
            # 判断是否符合预期
            is_correct = (actual_blocked == expected_blocked)
            
            test_result = {
                "test_id": test_id,
                "image_file": filename,
                "file_path": file_path,
                "image_url": img_url,
                "expected": expected,
                "success": True,
                "response_time": round(end_time - start_time, 3),
                "is_blocked": actual_blocked,
                "expected_blocked": expected_blocked,
                "is_correct": is_correct,
                "detect_result": result.get("detect_result"),
                "label_code": result.get("label_code"),
                "error": result.get("error"),
                "upload_info": {
                    "cdn_url": img_url,
                    "remote_path": upload_result.get("path", "")
                },
                "timestamp": datetime.now().isoformat()
            }
            
            # 输出结果
            if actual_blocked:
                status = "🚫 被拦截"
                details = f" (违规类型: {test_result['label_code']}, 检测结果: {test_result['detect_result']})"
            else:
                status = "✅ 通过"
                details = ""
            
            # 预期符合性检查
            if is_correct:
                expectation_status = "✅ 符合预期"
            else:
                expectation_status = "❌ 不符合预期"
                if expected_blocked:
                    expectation_status += " (应被拦截但未拦截)"
                else:
                    expectation_status += " (应通过但被拦截)"
            
            print(f"   结果: {status}{details}")
            print(f"   预期: {expectation_status} (耗时: {test_result['response_time']}s)")
            
            return test_result
            
        except Exception as e:
            end_time = time.time()
            test_result = {
                "test_id": test_id,
                "image_file": filename,
                "file_path": file_path,
                "image_url": img_url,
                "expected": expected,
                "success": False,
                "response_time": round(end_time - start_time, 3),
                "is_correct": False,
                "error": str(e),
                "upload_info": {
                    "cdn_url": img_url,
                    "remote_path": upload_result.get("path", "")
                },
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"   结果: ❌ 测试失败 - {e} (耗时: {test_result['response_time']}s)")
            return test_result

    def run_nickname_tests(self) -> None:
        """运行昵称测试"""
        print("\n🏷️  开始昵称违规检测测试")
        print("=" * 60)
        
        nicknames = self.load_nicknames()
        if not nicknames:
            print("⚠️  没有找到昵称测试数据，跳过昵称测试")
            return
        
        for i, nickname_data in enumerate(nicknames, 1):
            test_result = self.test_nickname(nickname_data, i)
            self.results["nickname_tests"].append(test_result)
            
            # 添加间隔避免请求过于频繁
            if i < len(nicknames):
                time.sleep(0.5)
        
        print(f"\n✅ 昵称测试完成，共测试 {len(nicknames)} 个用例")

    def run_avatar_tests(self) -> None:
        """运行头像测试"""
        print("\n🖼️  开始头像违规检测测试")
        print("=" * 60)
        
        avatar_files = self.load_avatar_files()
        if not avatar_files:
            print("⚠️  没有找到头像测试数据，跳过头像测试")
            return
        
        for i, avatar_data in enumerate(avatar_files, 1):
            test_result = self.test_avatar(avatar_data, i)
            self.results["avatar_tests"].append(test_result)
            
            # 添加间隔避免请求过于频繁
            if i < len(avatar_files):
                time.sleep(1.0)  # CDN上传需要更多时间间隔
        
        print(f"\n✅ 头像测试完成，共测试 {len(avatar_files)} 个用例")

    def generate_summary(self) -> None:
        """生成测试摘要"""
        nickname_tests = self.results["nickname_tests"]
        avatar_tests = self.results["avatar_tests"]
        
        # 昵称测试统计
        nickname_summary = {
            "total": len(nickname_tests),
            "success": len([t for t in nickname_tests if t.get("success", False)]),
            "failed": len([t for t in nickname_tests if not t.get("success", False)]),
            "blocked": len([t for t in nickname_tests if t.get("is_blocked", False)]),
            "passed": len([t for t in nickname_tests if t.get("success", False) and not t.get("is_blocked", False)]),
            "correct": len([t for t in nickname_tests if t.get("is_correct", False)]),
            "incorrect": len([t for t in nickname_tests if t.get("success", False) and not t.get("is_correct", False)]),
            "expected_blocked": len([t for t in nickname_tests if t.get("expected") == "BLOCK"]),
            "expected_passed": len([t for t in nickname_tests if t.get("expected") == "PASS"]),
            "avg_response_time": 0,
            "violation_types": {}
        }
        
        if nickname_tests:
            nickname_summary["avg_response_time"] = round(
                sum(t.get("response_time", 0) for t in nickname_tests) / len(nickname_tests), 3
            )
            
            # 统计违规类型
            for test in nickname_tests:
                if test.get("is_blocked") and test.get("label_code"):
                    label = test["label_code"]
                    nickname_summary["violation_types"][label] = nickname_summary["violation_types"].get(label, 0) + 1
        
        # 头像测试统计
        avatar_summary = {
            "total": len(avatar_tests),
            "success": len([t for t in avatar_tests if t.get("success", False)]),
            "failed": len([t for t in avatar_tests if not t.get("success", False)]),
            "blocked": len([t for t in avatar_tests if t.get("is_blocked", False)]),
            "passed": len([t for t in avatar_tests if t.get("success", False) and not t.get("is_blocked", False)]),
            "correct": len([t for t in avatar_tests if t.get("is_correct", False)]),
            "incorrect": len([t for t in avatar_tests if t.get("success", False) and not t.get("is_correct", False)]),
            "expected_blocked": len([t for t in avatar_tests if t.get("expected") == "BLOCK"]),
            "expected_passed": len([t for t in avatar_tests if t.get("expected") == "PASS"]),
            "avg_response_time": 0,
            "violation_types": {}
        }
        
        if avatar_tests:
            avatar_summary["avg_response_time"] = round(
                sum(t.get("response_time", 0) for t in avatar_tests) / len(avatar_tests), 3
            )
            
            # 统计违规类型
            for test in avatar_tests:
                if test.get("is_blocked") and test.get("label_code"):
                    label = test["label_code"]
                    avatar_summary["violation_types"][label] = avatar_summary["violation_types"].get(label, 0) + 1
        
        self.results["summary"] = {
            "nickname_summary": nickname_summary,
            "avatar_summary": avatar_summary,
            "test_time": datetime.now().isoformat(),
            "policy_check_config": {
                "enabled": self.policy_check.enabled,
                "base_url": self.policy_check.base_url,
                "timeout": self.policy_check.timeout,
                "scene": self.policy_check.scene
            }
        }

    def print_summary(self) -> None:
        """打印测试摘要"""
        summary = self.results["summary"]
        nickname_summary = summary["nickname_summary"]
        avatar_summary = summary["avatar_summary"]
        
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)
        
        # 昵称测试摘要
        if nickname_summary["total"] > 0:
            print(f"\n🏷️  昵称测试结果:")
            print(f"   总测试数: {nickname_summary['total']}")
            print(f"   成功测试: {nickname_summary['success']} ({nickname_summary['success']/nickname_summary['total']*100:.1f}%)")
            print(f"   失败测试: {nickname_summary['failed']} ({nickname_summary['failed']/nickname_summary['total']*100:.1f}%)")
            print(f"   被拦截: {nickname_summary['blocked']} ({nickname_summary['blocked']/nickname_summary['total']*100:.1f}%)")
            print(f"   通过检测: {nickname_summary['passed']} ({nickname_summary['passed']/nickname_summary['total']*100:.1f}%)")
            print(f"   符合预期: {nickname_summary['correct']} ({nickname_summary['correct']/nickname_summary['total']*100:.1f}%)")
            print(f"   不符合预期: {nickname_summary['incorrect']} ({nickname_summary['incorrect']/nickname_summary['total']*100:.1f}%)")
            print(f"   预期拦截: {nickname_summary['expected_blocked']}, 预期通过: {nickname_summary['expected_passed']}")
            print(f"   平均响应时间: {nickname_summary['avg_response_time']}秒")
            
            if nickname_summary["violation_types"]:
                print(f"   违规类型分布:")
                for label, count in nickname_summary["violation_types"].items():
                    print(f"     - {label}: {count}次")
        
        # 头像测试摘要
        if avatar_summary["total"] > 0:
            print(f"\n🖼️  头像测试结果:")
            print(f"   总测试数: {avatar_summary['total']}")
            print(f"   成功测试: {avatar_summary['success']} ({avatar_summary['success']/avatar_summary['total']*100:.1f}%)")
            print(f"   失败测试: {avatar_summary['failed']} ({avatar_summary['failed']/avatar_summary['total']*100:.1f}%)")
            print(f"   被拦截: {avatar_summary['blocked']} ({avatar_summary['blocked']/avatar_summary['total']*100:.1f}%)")
            print(f"   通过检测: {avatar_summary['passed']} ({avatar_summary['passed']/avatar_summary['total']*100:.1f}%)")
            print(f"   符合预期: {avatar_summary['correct']} ({avatar_summary['correct']/avatar_summary['total']*100:.1f}%)")
            print(f"   不符合预期: {avatar_summary['incorrect']} ({avatar_summary['incorrect']/avatar_summary['total']*100:.1f}%)")
            print(f"   预期拦截: {avatar_summary['expected_blocked']}, 预期通过: {avatar_summary['expected_passed']}")
            print(f"   平均响应时间: {avatar_summary['avg_response_time']}秒")
            
            if avatar_summary["violation_types"]:
                print(f"   违规类型分布:")
                for label, count in avatar_summary["violation_types"].items():
                    print(f"     - {label}: {count}次")
        
        # 总体状态
        total_tests = nickname_summary["total"] + avatar_summary["total"]
        total_success = nickname_summary["success"] + avatar_summary["success"]
        total_blocked = nickname_summary["blocked"] + avatar_summary["blocked"]
        total_correct = nickname_summary.get("correct", 0) + avatar_summary.get("correct", 0)
        
        if total_tests > 0:
            print(f"\n📈 总体统计:")
            print(f"   总测试数: {total_tests}")
            print(f"   成功率: {total_success/total_tests*100:.1f}%")
            print(f"   拦截率: {total_blocked/total_tests*100:.1f}%")
            print(f"   整体预期准确率: {total_correct/total_tests*100:.1f}%")
            if nickname_summary["total"] > 0:
                print(f"   昵称预期准确率: {nickname_summary['correct']/nickname_summary['total']*100:.1f}%")
            if avatar_summary["total"] > 0:
                print(f"   头像预期准确率: {avatar_summary['correct']/avatar_summary['total']*100:.1f}%")

    def save_results(self, output_file: str = None) -> None:
        """保存测试结果到JSON文件"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.test_dir / f"test_results_{timestamp}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"\n❌ 保存测试结果失败: {e}")

    def run_all_tests(self, save_results: bool = True) -> None:
        """运行所有测试"""
        print("🚀 开始Policy Check单元测试")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查PolicyCheck是否启用
        if not self.policy_check.enabled:
            print("⚠️  警告: PolicyCheck未启用，测试结果可能不准确")
        
        # 运行测试
        self.run_nickname_tests()
        self.run_avatar_tests()
        
        # 生成摘要
        self.generate_summary()
        self.print_summary()
        
        # 保存结果
        if save_results:
            self.save_results()
        
        print(f"\n🎉 所有测试完成！")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Policy Check 单元测试脚本')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--no-save', action='store_true', help='不保存测试结果到文件')
    parser.add_argument('--nicknames-only', action='store_true', help='只测试昵称')
    parser.add_argument('--avatars-only', action='store_true', help='只测试头像')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = PolicyCheckTester(args.config)
    
    # 运行指定的测试
    if args.nicknames_only:
        tester.run_nickname_tests()
        tester.generate_summary()
        tester.print_summary()
        if not args.no_save:
            tester.save_results()
    elif args.avatars_only:
        tester.run_avatar_tests()
        tester.generate_summary()
        tester.print_summary()
        if not args.no_save:
            tester.save_results()
    else:
        # 运行所有测试
        tester.run_all_tests(not args.no_save)


if __name__ == "__main__":
    main()