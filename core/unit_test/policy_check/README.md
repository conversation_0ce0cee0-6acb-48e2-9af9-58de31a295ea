# Policy Check 单元测试

这个目录包含了对 `core/policy_check.py` 模块的单元测试，用于验证头像图片和昵称的违规检测功能。

## 文件结构

```
core/unit_test/policy_check/
├── README.md                 # 说明文档  
├── test_policy_check.py      # 主测试脚本
├── nick_names.csv           # 昵称测试数据（CSV格式）
├── avatars.csv              # 头像测试配置（CSV格式）
├── avatars/                 # 头像测试图片目录
│   ├── test01.jpeg
│   ├── test02.jpeg
│   ├── test03.jpeg
│   └── test04.jpeg
└── test_results_*.json      # 测试结果文件（自动生成）
```

## 测试内容

### 1. 昵称违规检测测试
- 读取 `nick_names.csv` 文件中的昵称列表和预期结果
- 调用 `PolicyCheck.check_user_nickname()` 方法
- 使用 `scene=daziUser` 参数
- 对比实际结果与预期结果，统计准确率

### 2. 头像图片违规检测测试
- 读取 `avatars.csv` 配置文件中的图片路径和预期结果
- 自动上传图片文件到CDN获取可访问URL
- 调用 `PolicyCheck.check_user_avatar()` 方法进行违规检测
- 使用 `scene=daziUser` 参数
- 对比实际结果与预期结果，统计准确率和响应时间

## 使用方法

### 基本用法

```bash
# 运行所有测试
python core/unit_test/policy_check/test_policy_check.py

# 指定配置文件
python core/unit_test/policy_check/test_policy_check.py --config data/config.json

# 只测试昵称
python core/unit_test/policy_check/test_policy_check.py --nicknames-only

# 只测试头像
python core/unit_test/policy_check/test_policy_check.py --avatars-only

# 不保存结果文件
python core/unit_test/policy_check/test_policy_check.py --no-save
```

### 命令行参数

- `--config`, `-c`: 指定配置文件路径
- `--no-save`: 不保存测试结果到JSON文件
- `--nicknames-only`: 只运行昵称测试
- `--avatars-only`: 只运行头像测试

## 测试数据

### 昵称测试数据 (nick_names.csv)

CSV格式，包含昵称和预期结果：

示例：
```csv
习近平,BLOCK
习大大,BLOCK
维尼熊,BLOCK
特朗普,PASS
川建国,PASS
拜登,PASS
刘德华,PASS
```

**格式说明：**
- 第1列：昵称内容
- 第2列：预期结果（BLOCK=应被拦截，PASS=应通过）

### 头像测试数据 (avatars.csv)

CSV格式，包含图片路径和预期结果：

示例：
```csv
avatars/test01.jpeg,BLOCK
avatars/test02.jpeg,BLOCK
avatars/test03.jpeg,BLOCK
avatars/test04.jpeg,PASS
```

**格式说明：**
- 第1列：图片文件路径（相对于测试目录）
- 第2列：预期结果（BLOCK=应被拦截，PASS=应通过）

**支持的图片格式：**
- `.jpg`, `.jpeg`
- `.png`
- `.webp`
- `.gif`
- `.tiff`, `.tif`
- `.heif`

## 测试结果

### 控制台输出

测试过程中会实时显示：
- 每个测试用例的结果
- 检测状态（通过/被拦截）
- **预期符合性**（✅ 符合预期 / ❌ 不符合预期）
- 违规类型和原因
- 响应时间
- CDN上传状态（头像测试）

**示例输出：**
```
🔍 测试昵称 #1: 习近平 (预期: BLOCK)
   结果: ✅ 通过
   预期: ❌ 不符合预期 (应被拦截但未拦截) (耗时: 0.14s)

🔍 测试头像 #1: test01.jpeg (预期: BLOCK)
   📤 上传图片到CDN...
   📤 CDN上传成功: https://image.supamob.com.cn/...
   结果: ✅ 通过
   预期: ❌ 不符合预期 (应被拦截但未拦截) (耗时: 1.2s)
```

### 测试报告

测试完成后会显示：
- 总体统计信息
- 成功率和拦截率
- **预期准确率**（符合预期的百分比）
- 平均响应时间
- 违规类型分布

### JSON结果文件

自动生成格式为 `test_results_YYYYMMDD_HHMMSS.json` 的结果文件，包含：

```json
{
  "nickname_tests": [
    {
      "test_id": 1,
      "nickname": "XXX",
      "expected": "BLOCK",
      "success": true,
      "response_time": 0.234,
      "is_blocked": false,
      "expected_blocked": true,
      "is_correct": false,
      "detect_result": "PASS",
      "label_code": null,
      "timestamp": "2025-06-24T12:00:00"
    }
  ],
  "avatar_tests": [
    {
      "test_id": 1,
      "image_file": "test01.jpeg",
      "file_path": "/path/to/test01.jpeg",
      "image_url": "https://image.supamob.com.cn/...",
      "expected": "BLOCK",
      "success": true,
      "response_time": 1.567,
      "is_blocked": false,
      "expected_blocked": true,
      "is_correct": false,
      "detect_result": "PASS",
      "label_code": null,
      "upload_info": {
        "cdn_url": "https://image.supamob.com.cn/...",
        "remote_path": "test/avatars/1750746000_test01.jpeg"
      },
      "timestamp": "2025-06-24T12:00:01"
    }
  ],
  "summary": {
    "nickname_summary": {
      "total": 7,
      "success": 7,
      "failed": 0,
      "blocked": 0,
      "passed": 7,
      "correct": 4,
      "incorrect": 3,
      "expected_blocked": 3,
      "expected_passed": 4,
      "avg_response_time": 0.345,
      "violation_types": {}
    },
    "avatar_summary": {
      "total": 4,
      "success": 4,
      "failed": 0,
      "blocked": 0,
      "passed": 4,
      "correct": 1,
      "incorrect": 3,
      "expected_blocked": 3,
      "expected_passed": 1,
      "avg_response_time": 1.234,
      "violation_types": {}
    }
  }
}
```

## 注意事项

### 1. 配置要求

测试需要正确配置 `policy_check` 部分：

```json
{
  "policy_check": {
    "enabled": true,
    "base_url": "https://ai-guardian-api.apusai.com",
    "api_key": "your-api-key-here",
    "timeout": 10,
    "scene": "dazi"
  }
}
```

### 2. CDN上传功能

测试脚本已集成CDN上传功能，会自动将本地图片上传到CDN并获取可访问的URL进行检测。需要确保CDN服务配置正确。

### 3. API限制

- 请注意API调用频率限制
- 测试脚本在请求间添加了0.5秒延迟
- 超时时间设置为10秒（图片检测建议值）

### 4. 测试数据准备

- 确保 `nick_names.csv` 包含有代表性的测试用例和预期结果
- 确保 `avatars.csv` 配置正确的图片路径和预期结果
- 确保 `avatars/` 目录包含不同类型的图片
- 测试用例应覆盖正常和违规内容

## 扩展测试

可以通过以下方式扩展测试：

1. **添加更多测试用例**：在 `nick_names.csv` 中添加更多昵称和预期结果
2. **添加更多图片**：在 `avatars/` 目录添加更多测试图片，并在 `avatars.csv` 中配置相应预期结果
3. **自定义测试场景**：修改 `scene` 参数测试不同场景
4. **性能测试**：增加并发测试功能
5. **集成测试**：结合HTTP API进行端到端测试

## 故障排除

### 常见问题

1. **API密钥未配置**
   ```
   ⚠️  警告: PolicyCheck未启用，测试结果可能不准确
   ```
   解决：检查配置文件中的 `api_key` 设置

2. **网络连接失败**
   ```
   ❌ 测试失败 - 请求失败: Connection timeout
   ```
   解决：检查网络连接和API地址

3. **CDN上传失败**
   ```
   ❌ CDN上传失败 - 上传异常: ...
   ```
   解决：检查CDN服务配置和网络连接

4. **配置文件未找到**
   ```
   ⚠️  警告: 未找到配置文件，将使用默认设置
   ```
   解决：指定正确的配置文件路径或创建默认配置文件