# MP3 会议记录转写工具

这个工具使用 Google Gemini 2.5 Flash 模型来处理 MP3 音频文件，自动生成会议纪要和详细的发言记录。

## 功能特点

- 🎵 支持 MP3 音频文件处理
- 📝 自动生成标准格式的会议纪要
- 💬 生成详细的发言转录记录
- 🔍 智能识别发言人和关键信息
- 📄 输出 Markdown 格式的结果文件

## 安装依赖

```bash
pip install google-genai
```

## 使用方法

### 基本用法

```bash
python mp3_summarize.py /path/to/meeting.mp3 --api-key YOUR_GEMINI_API_KEY
```

### 完整参数

```bash
python mp3_summarize.py meeting.mp3 \
  --api-key YOUR_GEMINI_API_KEY \
  --model gemini-2.5-flash \
  --output-dir ./output
```

## 参数说明

- `mp3_file`: MP3 音频文件路径（必需）
- `--api-key`: Google Gemini API Key（必需）
- `--model`: Gemini 模型名称（可选，默认: gemini-2.5-flash）
- `--output-dir`: 输出目录（可选，默认与 MP3 文件同目录）

## 输出文件

工具会生成两个 Markdown 文件：

1. `{文件名}_会议纪要_{时间戳}.md` - 结构化的会议纪要
2. `{文件名}_发言记录_{时间戳}.md` - 详细的发言转录

## 文件限制

- 支持格式：MP3
- 最大文件大小：100MB
- 处理超时：2分钟

## 示例输出

### 会议纪要格式
```markdown
# 会议纪要

## 基本信息
- 会议时间：2024-06-25 14:30
- 会议主题：产品开发进度讨论
- 参会人员：张三、李四、王五

## 主要讨论内容
- 产品功能需求确认
- 开发进度和时间安排
- 测试计划制定

## 决议事项
- 确定产品发布时间为下月15日
- 增加用户体验测试环节

## 行动计划
- 张三：完成核心功能开发（截止时间：本周五）
- 李四：准备测试用例（截止时间：下周二）
```

### 发言记录格式
```markdown
# 会议发言详细记录

## 详细转录

### [00:00-02:15] 张三：
大家好，今天我们主要讨论一下产品开发的进度情况...

### [02:16-04:30] 李四：
关于测试方面，我觉得我们需要重点关注用户体验...
```

## 注意事项

1. 需要有效的 Google Gemini API Key
2. 确保网络连接稳定
3. 音频质量越好，转录效果越佳
4. 处理大文件时请耐心等待

## 故障排除

- **API Key 错误**: 检查 API Key 是否正确且有效
- **文件太大**: 尝试压缩音频文件或分段处理
- **网络超时**: 检查网络连接，必要时重试
- **音频格式**: 确保文件是有效的 MP3 格式