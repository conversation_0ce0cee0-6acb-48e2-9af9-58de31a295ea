#!/usr/bin/env python3
"""
MP3 会议记录转写和摘要工具
使用 Google Gemini 2.5 Flash 模型处理音频文件，生成会议纪要
"""

import os
import sys
import argparse
import asyncio
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from google import genai
from google.genai import types
from config.logger import setup_logging

# 配置日志
logger = setup_logging()
TAG = __name__

class MP3Summarizer:
    """MP3 会议记录处理器"""
    
    def __init__(self, api_key: str, model: str = "gemini-2.5-flash"):
        """初始化处理器"""
        self.api_key = api_key
        self.model = model
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        logger.bind(tag=TAG).info(f"初始化MP3Summarizer - 模型: {self.model}")
        logger.bind(tag=TAG).info(f"API Key长度: {len(self.api_key)} 字符")
        
        # 初始化 Gemini 客户端，增加超时时间到5分钟
        try:
            self.client = genai.Client(
                api_key=self.api_key,
                http_options=types.HttpOptions(timeout=300000)  # 5分钟超时
            )
            logger.bind(tag=TAG).info("Gemini客户端初始化成功")
        except Exception as e:
            logger.bind(tag=TAG).error(f"Gemini客户端初始化失败: {str(e)}")
            raise
        
        logger.bind(tag=TAG).info(f"MP3Summarizer initialized with model: {self.model}")
    
    def validate_mp3_file(self, file_path: str) -> bool:
        """验证MP3文件"""
        if not os.path.exists(file_path):
            logger.bind(tag=TAG).error(f"文件不存在: {file_path}")
            return False
        
        if not file_path.lower().endswith('.mp3'):
            logger.bind(tag=TAG).error(f"文件不是MP3格式: {file_path}")
            return False
        
        file_size = os.path.getsize(file_path)
        max_size = 100 * 1024 * 1024  # 100MB限制
        
        if file_size > max_size:
            logger.bind(tag=TAG).error(f"文件太大: {file_size} bytes (最大 {max_size} bytes)")
            return False
        
        logger.bind(tag=TAG).info(f"MP3文件验证通过: {file_path} ({file_size} bytes)")
        return True
    
    async def process_mp3(self, mp3_path: str, output_dir: str = None) -> dict:
        """处理MP3文件并生成会议纪要"""
        try:
            # 验证文件
            if not self.validate_mp3_file(mp3_path):
                return {"success": False, "error": "文件验证失败"}
            
            # 设置输出目录
            if not output_dir:
                output_dir = os.path.dirname(mp3_path)
            
            os.makedirs(output_dir, exist_ok=True)
            
            # 获取当前时间作为会议时间
            meeting_time = datetime.now()
            logger.bind(tag=TAG).info(f"使用当前时间作为会议时间: {meeting_time.strftime('%Y年%m月%d日 %H:%M')}")
            
            # 上传音频文件到Gemini
            logger.bind(tag=TAG).info(f"开始上传音频文件到Gemini: {mp3_path}")
            display_name = os.path.basename(mp3_path)
            
            try:
                uploaded_file = await self.client.aio.files.upload(file=mp3_path)
                logger.bind(tag=TAG).info(f"文件上传成功: {uploaded_file.name}")
                logger.bind(tag=TAG).info(f"文件URI: {uploaded_file.uri}")
            except Exception as e:
                logger.bind(tag=TAG).error(f"文件上传失败: {str(e)}")
                raise
            
            # 生成会议纪要
            logger.bind(tag=TAG).info("开始生成会议纪要...")
            start_time = datetime.now()
            summary_result = await self._generate_summary(uploaded_file, meeting_time)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.bind(tag=TAG).info(f"会议纪要生成耗时: {duration:.2f} 秒")
            
            # 保存结果到文件
            results = await self._save_results(
                mp3_path, output_dir, summary_result, meeting_time
            )
            
            return {
                "success": True,
                "files": results,
                "summary_length": len(summary_result)
            }
            
        except Exception as e:
            error_msg = f"处理MP3文件时发生错误: {str(e)}"
            logger.bind(tag=TAG).error(error_msg)
            return {"success": False, "error": error_msg}
    
    async def _generate_summary(self, uploaded_file, meeting_time: datetime) -> str:
        """生成会议纪要"""
        meeting_time_str = meeting_time.strftime("%Y年%m月%d日 %H:%M")
        
        system_prompt = f"""你是一个专业的会议纪要整理助手，需要将音频内容整理成规范的会议纪要。

要求：
1. 仔细听取音频内容，提取关键信息
2. 按照标准会议纪要格式组织内容
3. 包括：会议主题、主要讨论内容、决议事项、行动计划等
4. 语言要简洁明了，条理清晰
5. 突出重点决议和后续行动
6. 如果音频质量不佳或信息不完整，请根据现有信息尽力整理
7. 会议时间请固定使用：{meeting_time_str}

会议纪要格式：
# 会议纪要

## 基本信息
- 会议时间：{meeting_time_str}
- 会议主题：[从音频中提取]

## 主要讨论内容
[按要点列出主要讨论的内容]

## 决议事项
[列出会议中达成的决议]

## 行动计划
[列出后续需要执行的行动项目，包括负责人和时间节点]

## 其他事项
[其他需要记录的内容]

请严格按照以上格式整理会议纪要，特别注意会议时间必须使用指定的时间。"""

        user_prompt = f"请分析这段会议音频，生成完整的会议纪要。会议时间请使用：{meeting_time_str}"

        try:
            logger.bind(tag=TAG).info("开始创建生成配置...")
            # 创建生成配置
            generation_config = types.GenerateContentConfig(
                system_instruction=system_prompt,
                thinking_config=types.ThinkingConfig(thinking_budget=0)
            )
            logger.bind(tag=TAG).info("生成配置创建成功")

            logger.bind(tag=TAG).info(f"开始调用Gemini API - 模型: {self.model}")
            logger.bind(tag=TAG).info(f"内容包含: 用户提示词({len(user_prompt)}字符) + 上传的音频文件({uploaded_file.name})")
            
            # 生成内容
            response = await self.client.aio.models.generate_content(
                model=self.model,
                contents=[user_prompt, uploaded_file],
                config=generation_config
            )

            logger.bind(tag=TAG).info("Gemini API调用完成")
            logger.bind(tag=TAG).info(f"响应对象类型: {type(response)}")
            logger.bind(tag=TAG).info(f"响应是否有text属性: {hasattr(response, 'text')}")
            
            if hasattr(response, 'text') and response.text:
                logger.bind(tag=TAG).info(f"会议纪要生成成功，长度: {len(response.text)} 字符")
                return response.text
            else:
                logger.bind(tag=TAG).warning("响应中没有text内容")
                return "无法生成会议纪要"

        except Exception as e:
            logger.bind(tag=TAG).error(f"生成会议纪要失败: {str(e)}")
            logger.bind(tag=TAG).error(f"异常类型: {type(e)}")
            import traceback
            logger.bind(tag=TAG).error(f"完整错误堆栈: {traceback.format_exc()}")
            return f"生成会议纪要时发生错误：{str(e)}"


    async def _save_results(self, mp3_path: str, output_dir: str, summary: str, meeting_time: datetime) -> list:
        """保存结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(mp3_path))[0]
        
        files = []
        
        try:
            # 保存会议纪要
            summary_file = os.path.join(output_dir, f"{base_name}_会议纪要_{timestamp}.md")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            
            files.append(summary_file)
            logger.bind(tag=TAG).info(f"会议纪要已保存: {summary_file}")
            
        except Exception as e:
            logger.bind(tag=TAG).error(f"保存文件失败: {str(e)}")
            raise
        
        return files


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MP3 会议记录转写和摘要工具")
    parser.add_argument("mp3_file", help="MP3 音频文件路径")
    parser.add_argument("--api-key", required=True, help="Google Gemini API Key")
    parser.add_argument("--model", default="gemini-2.5-flash", help="Gemini 模型名称 (默认: gemini-2.5-flash)")
    parser.add_argument("--output-dir", help="输出目录 (默认: 与MP3文件同目录)")
    
    args = parser.parse_args()
    
    # 验证MP3文件路径
    if not os.path.exists(args.mp3_file):
        print(f"错误: 文件不存在 - {args.mp3_file}")
        sys.exit(1)
    
    # 创建处理器
    try:
        summarizer = MP3Summarizer(api_key=args.api_key, model=args.model)
    except Exception as e:
        print(f"错误: 初始化失败 - {str(e)}")
        sys.exit(1)
    
    # 处理文件
    print(f"开始处理MP3文件: {args.mp3_file}")
    print(f"使用模型: {args.model}")
    print("=" * 50)
    
    result = await summarizer.process_mp3(
        mp3_path=args.mp3_file,
        output_dir=args.output_dir
    )
    
    if result["success"]:
        print("✅ 处理完成！")
        print(f"📄 会议纪要长度: {result['summary_length']} 字符")
        print("\n生成的文件:")
        for file_path in result["files"]:
            print(f"  - {file_path}")
    else:
        print(f"❌ 处理失败: {result['error']}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())