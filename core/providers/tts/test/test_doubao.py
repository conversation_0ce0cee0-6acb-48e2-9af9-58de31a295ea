
import os
import sys
import yaml
import asyncio

# Add project root to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))

from core.providers.tts.doubao import TTSProvider

def load_config():
    config_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../data/.config.yaml'))
    if not os.path.exists(config_path):
        print(f"Error: Config file not found at {config_path}")
        sys.exit(1)
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

async def main():
    config = load_config()
    doubao_config = config.get('TTS', {}).get('DoubaoTTS')
    if not doubao_config:
        print("Error: DoubaoTTS config not found in data/.config.yaml")
        sys.exit(1)

    # Ensure the output directory exists
    output_dir = doubao_config.get('output_dir', 'tmp')
    os.makedirs(output_dir, exist_ok=True)

    tts_provider = TTSProvider(doubao_config, delete_audio_file=False)

    text = input("Enter the text to synthesize: ")
    voice_id = input("Enter the voice ID (e.g., zh_female_linjianvhai_moon_bigtts): ")

    # Override the default voice with the user-provided one
    tts_provider.voice = voice_id

    output_file = tts_provider.generate_filename(extension=".mp3")

    try:
        print(f"Synthesizing speech for text: '{text}' with voice: '{voice_id}'")
        success = await tts_provider.text_to_speak(text, output_file)
        if success:
            print(f"Speech synthesized successfully: {output_file}")
            # Play the audio file
            if sys.platform == "darwin":
                os.system(f"afplay {output_file}")
            elif sys.platform == "linux":
                os.system(f"aplay {output_file}")
            else:
                print(f"Please play the audio file manually: {output_file}")
        else:
            print("Failed to synthesize speech.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(main())
