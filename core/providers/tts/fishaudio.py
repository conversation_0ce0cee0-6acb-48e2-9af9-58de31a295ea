import os
import uuid
from datetime import datetime
from typing import Optional
from core.utils.util import check_model_key
from core.providers.tts.base import TTSProviderBase
from config.logger import setup_logging

try:
    from fish_audio_sdk import Session, TTSRequest, ReferenceAudio, Prosody
    FISH_AUDIO_AVAILABLE = True
except ImportError:
    FISH_AUDIO_AVAILABLE = False

TAG = __name__
logger = setup_logging()


class TTSProvider(TTSProviderBase):
    
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        
        if not FISH_AUDIO_AVAILABLE:
            raise Exception("Fish Audio SDK未安装，请运行: pip install fish-audio-sdk")
        
        self.api_key = config.get("api_key")
        self.base_url = config.get("base_url", "https://api.fish.audio")
        self.reference_id = config.get("reference_id")
        
        # 音频格式配置
        self.format = config.get("format", "mp3")
        self.sample_rate = config.get("sample_rate")
        self.mp3_bitrate = int(config.get("mp3_bitrate", 128))
        
        # TTS参数
        self.chunk_length = int(config.get("chunk_length", 200))
        self.normalize = str(config.get("normalize", True)).lower() in ("true", "1", "yes")
        self.latency = config.get("latency", "balanced")
        
        # 语音控制参数
        self.temperature = float(config.get("temperature", 0.7))
        self.top_p = float(config.get("top_p", 0.7))
        
        # 语调控制
        self.speed = float(config.get("speed", 1.0))
        self.volume = float(config.get("volume", 0.0))
        
        # 音频转换配置
        self.output_format = config.get("output_format", "wav")  # 客户端需要的格式
        
        # 初始化 voice 属性，用于接收来自 roles.yaml 的动态配置
        self.voice = None  # 这个属性会被 user_profile_manager.py 动态设置

        # 使用的模型
        self.model = config.get("backend", "s1")
        
        check_model_key("Fish Audio TTS", self.api_key)

    def generate_filename(self, extension=None):
        if extension is None:
            # 根据output_format设置扩展名（客户端需要的格式）
            extension = f".{self.output_format}"
        return os.path.join(
            self.output_file,
            f"tts-{datetime.now().date()}@{uuid.uuid4().hex}{extension}",
        )

    async def text_to_speak(self, text, output_file, client_id: str = None, connection=None):
        """使用Fish Audio SDK进行语音合成"""
        if not client_id:
            client_id = "default"
        
        # 为每个TTS请求生成唯一的请求ID
        request_id = str(uuid.uuid4())[:8]  # 使用UUID的前8位作为请求ID
        request_tag = f"{client_id}:{request_id}"
            
        logger.bind(tag=TAG).info(f"[{request_tag}] 开始Fish Audio TTS合成: {text[:50]}...")
        
        try:
            # 动态获取reference_id，优先级：
            # 1. 实例的voice属性（从roles.yaml动态设置）
            # 2. 配置中的reference_id（静态配置）
            reference_id_to_use = None
            
            # 检查是否有动态设置的voice属性（来自roles.yaml）
            if hasattr(self, 'voice') and self.voice:
                reference_id_to_use = self.voice
                logger.bind(tag=TAG).info(f"[{request_tag}] 使用角色配置的voice作为reference_id: {reference_id_to_use}")
            elif self.reference_id:
                reference_id_to_use = self.reference_id
                logger.bind(tag=TAG).info(f"[{request_tag}] 使用静态配置的reference_id: {reference_id_to_use}")
            
            # 记录传递给Fish Audio的reference_id
            logger.bind(tag=TAG).info(f"[{request_tag}] 🎵 传递给Fish Audio TTS的reference_id: {reference_id_to_use or '未设置'}")
            
            # 准备TTS请求参数
            request_params = {
                "text": text,
                "format": self.format,
                "normalize": self.normalize,
                "latency": self.latency,
                "temperature": self.temperature,
                "top_p": self.top_p,
            }
            
            # 设置采样率
            if self.sample_rate:
                request_params["sample_rate"] = int(self.sample_rate)
            
            # 设置 mp3 比特率
            if self.format.lower() == "mp3":
                request_params["mp3_bitrate"] = self.mp3_bitrate
            
            # 设置参考ID
            if reference_id_to_use:
                request_params["reference_id"] = reference_id_to_use
            
            # 设置语调控制
            if self.speed != 1.0 or self.volume != 0.0:
                request_params["prosody"] = Prosody(speed=self.speed, volume=self.volume)
                logger.bind(tag=TAG).info(f"[{request_tag}] 语调控制: speed={self.speed}, volume={self.volume}")
            
            # 创建TTS请求
            tts_request = TTSRequest(**{k: v for k, v in request_params.items() if v is not None})
            
            logger.bind(tag=TAG).debug(f"[{request_tag}] Fish Audio TTS完整请求参数: {request_params}")
            
            # 收集音频数据
            audio_chunks = []
            chunk_count = 0
            
            logger.bind(tag=TAG).info(f"[{request_tag}] 🎯 开始TTS请求处理")
            
            # 为每个请求创建独立的Session，避免并发冲突
            async with Session(apikey=self.api_key, base_url=self.base_url) as session:
                async for chunk in session.tts.awaitable(tts_request, backend=self.model):
                    chunk_count += 1
                    audio_chunks.append(chunk)
                    logger.bind(tag=TAG).debug(f"[{request_tag}] 收到音频块 #{chunk_count}: {len(chunk)}字节")
            
            logger.bind(tag=TAG).info(f"[{request_tag}] ✅ TTS请求处理完成，共{chunk_count}个音频块")
            
            # 合并所有音频块
            if audio_chunks:
                total_audio = b''.join(audio_chunks)
                logger.bind(tag=TAG).info(f"[{request_tag}] Fish Audio TTS合成完成: {text[:20]}..., 共收到{chunk_count}个音频块, 原始数据大小: {len(total_audio)}字节")
                
                # 如果客户端需要的格式与Fish Audio返回的格式不同，进行转换
                if self.output_format.lower() != self.format.lower():
                    converted_audio = await self._convert_audio_format(
                        total_audio, self.format, self.output_format, request_tag
                    )
                    if converted_audio:
                        total_audio = converted_audio
                        logger.bind(tag=TAG).info(f"[{request_tag}] 音频格式转换完成: {self.format} -> {self.output_format}, 转换后大小: {len(total_audio)}字节")
                    else:
                        logger.bind(tag=TAG).warning(f"[{request_tag}] 音频格式转换失败，使用原始格式")
                
                # 保存到文件
                with open(output_file, "wb") as f:
                    f.write(total_audio)
                logger.bind(tag=TAG).info(f"[{request_tag}] Fish Audio TTS生成成功: {text[:20]}..., 最终文件大小: {len(total_audio)}字节")
            else:
                raise Exception("未收到音频数据")
                
        except ImportError:
            raise Exception("Fish Audio SDK未安装，请运行: pip install fish-audio-sdk")
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{request_tag}] Fish Audio TTS合成失败: {e}")
            raise Exception(f"Fish Audio TTS error: {e}")

    async def _convert_audio_format(self, audio_data: bytes, source_format: str, target_format: str, request_tag: str) -> Optional[bytes]:
        """
        将音频数据从一种格式转换为另一种格式
        
        Args:
            audio_data: 原始音频数据
            source_format: 源格式（如 "mp3"）
            target_format: 目标格式（如 "wav"）
            request_tag: 请求标识符（用于日志）
            
        Returns:
            bytes: 转换后的音频数据，转换失败返回None
        """
        try:
            import tempfile
            import asyncio
            
            logger.bind(tag=TAG).info(f"[{request_tag}] 开始音频格式转换: {source_format} -> {target_format}")
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=f".{source_format}", delete=False) as temp_input:
                temp_input.write(audio_data)
                temp_input_path = temp_input.name
            
            with tempfile.NamedTemporaryFile(suffix=f".{target_format}", delete=False) as temp_output:
                temp_output_path = temp_output.name
            
            try:
                # 使用 ffmpeg 进行格式转换
                cmd = [
                    "ffmpeg",
                    "-i", temp_input_path,
                    "-y",  # 覆盖输出文件
                    "-acodec", self._get_audio_codec(target_format),
                    "-ar", str(self.sample_rate) if self.sample_rate else "44100",  # 采样率
                    temp_output_path
                ]
                
                logger.bind(tag=TAG).debug(f"[{request_tag}] FFmpeg命令: {' '.join(cmd)}")
                
                # 执行转换 - 使用异步subprocess
                import asyncio
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(), 
                        timeout=2  # 2秒超时
                    )
                    result_returncode = process.returncode
                    result_stderr = stderr.decode('utf-8') if stderr else ""
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    logger.bind(tag=TAG).error(f"[{request_tag}] 音频格式转换超时")
                    return None
                
                if result_returncode == 0:
                    # 读取转换后的文件
                    with open(temp_output_path, "rb") as f:
                        converted_data = f.read()
                    
                    logger.bind(tag=TAG).info(f"[{request_tag}] 音频格式转换成功")
                    return converted_data
                else:
                    logger.bind(tag=TAG).error(f"[{request_tag}] FFmpeg转换失败: {result_stderr}")
                    return None
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_input_path)
                    os.unlink(temp_output_path)
                except:
                    pass
                    
        except ImportError:
            logger.bind(tag=TAG).warning(f"[{request_tag}] 缺少音频转换依赖，无法进行格式转换")
            return None
        except Exception as e:
            logger.bind(tag=TAG).error(f"[{request_tag}] 音频格式转换失败: {e}")
            return None
    
    def _get_audio_codec(self, format: str) -> str:
        """
        根据音频格式获取对应的编解码器
        
        Args:
            format: 音频格式
            
        Returns:
            str: 对应的编解码器
        """
        codec_map = {
            "wav": "pcm_s16le",
            "mp3": "libmp3lame", 
            "opus": "libopus",
            "aac": "aac",
            "ogg": "libvorbis",
            "flac": "flac"
        }
        return codec_map.get(format.lower(), "pcm_s16le")

    def __del__(self):
        """析构函数，清理资源"""
        pass