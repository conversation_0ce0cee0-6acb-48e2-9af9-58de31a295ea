#!/usr/bin/env python3
"""
WebSocket PING PONG时间间隔分析脚本
分析日志中PING PONG消息的时间间隔，检测超时情况
"""

import re
import sys
import argparse
from datetime import datetime, timedelta
from typing import List, Tuple, Optional


def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
    """解析日志时间戳"""
    try:
        # 格式: 2025-06-24 11:11:44
        return datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return None


def read_log_lines(log_file: str) -> List[str]:
    """读取日志文件所有行"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            return f.readlines()
    except FileNotFoundError:
        print(f"错误: 日志文件 {log_file} 未找到")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取日志文件失败 - {e}")
        sys.exit(1)


def extract_ping_pong_events(lines: List[str]) -> List[Tuple[int, datetime, str]]:
    """提取PING PONG事件及其行号和时间戳"""
    events = []
    # 修改正则表达式以匹配实际日志格式
    ping_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*< PING')
    pong_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*> PONG')
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检查PING消息
        ping_match = ping_pattern.search(line)
        if ping_match:
            timestamp = parse_timestamp(ping_match.group(1))
            if timestamp:
                events.append((i, timestamp, 'PING'))
        
        # 检查PONG消息
        pong_match = pong_pattern.search(line)
        if pong_match:
            timestamp = parse_timestamp(pong_match.group(1))
            if timestamp:
                events.append((i, timestamp, 'PONG'))
    
    return events


def extract_closing_events(lines: List[str]) -> List[Tuple[int, datetime]]:
    """提取连接关闭事件及其行号和时间戳"""
    closing_events = []
    closing_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*connection is CLOSING')
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检查连接关闭消息
        closing_match = closing_pattern.search(line)
        if closing_match:
            timestamp = parse_timestamp(closing_match.group(1))
            if timestamp:
                closing_events.append((i, timestamp))
    
    return closing_events


def extract_open_events(lines: List[str]) -> List[Tuple[int, datetime]]:
    """提取连接建立事件及其行号和时间戳"""
    open_events = []
    open_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*connection is OPEN')
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检查连接建立消息
        open_match = open_pattern.search(line)
        if open_match:
            timestamp = parse_timestamp(open_match.group(1))
            if timestamp:
                open_events.append((i, timestamp))
    
    return open_events


def analyze_ping_pong_intervals(events: List[Tuple[int, datetime, str]], 
                              timeout_threshold: float) -> List[Tuple[int, int, float, datetime, datetime]]:
    """分析PING PONG间隔，返回超时的情况"""
    timeouts = []
    
    i = 0
    while i < len(events) - 1:
        current_line, current_time, current_type = events[i]
        
        # 查找下一个PING事件
        if current_type == 'PING':
            # 查找对应的PONG
            next_i = i + 1
            while next_i < len(events):
                next_line, next_time, next_type = events[next_i]
                if next_type == 'PONG':
                    # 计算PING到PONG的时间间隔
                    interval = (next_time - current_time).total_seconds()
                    
                    if interval > timeout_threshold:
                        timeouts.append((current_line, next_line, interval, current_time, next_time))
                    break
                next_i += 1
        
        i += 1
    
    return timeouts


def analyze_closing_intervals(ping_pong_events: List[Tuple[int, datetime, str]], 
                            closing_events: List[Tuple[int, datetime]]) -> List[Tuple[datetime, str, datetime, float, int, int]]:
    """分析最后一次PING/PONG到连接关闭的时间间隔"""
    closing_intervals = []
    
    for closing_line, closing_time in closing_events:
        # 查找在此连接关闭之前最后一次的PING或PONG
        last_ping_pong = None
        
        for event_line, event_time, event_type in reversed(ping_pong_events):
            if event_time <= closing_time:  # 改为 <= 以包含同一时间的事件
                last_ping_pong = (event_line, event_time, event_type)
                break
        
        if last_ping_pong:
            event_line, event_time, event_type = last_ping_pong
            interval = (closing_time - event_time).total_seconds()
            closing_intervals.append((event_time, event_type, closing_time, interval, event_line, closing_line))
    
    return closing_intervals


def analyze_open_to_ping_intervals(ping_pong_events: List[Tuple[int, datetime, str]], 
                                 open_events: List[Tuple[int, datetime]]) -> List[Tuple[datetime, datetime, float, int, int]]:
    """分析连接建立到第一个PING的时间间隔"""
    open_to_ping_intervals = []
    
    for open_line, open_time in open_events:
        # 查找在此连接建立之后第一次的PING
        first_ping = None
        
        for event_line, event_time, event_type in ping_pong_events:
            if event_type == 'PING' and event_time >= open_time:
                first_ping = (event_line, event_time)
                break
        
        if first_ping:
            ping_line, ping_time = first_ping
            interval = (ping_time - open_time).total_seconds()
            open_to_ping_intervals.append((open_time, ping_time, interval, open_line, ping_line))
    
    return open_to_ping_intervals


def print_context_logs(lines: List[str], start_line: int, end_line: int, context_lines: int = 10):
    """打印指定行周围的相关日志（只显示连接、PING PONG相关的日志）"""
    total_lines = len(lines)
    
    # 计算上下文范围
    context_start = max(0, start_line - context_lines)
    context_end = min(total_lines, end_line + context_lines + 1)
    
    # 定义需要保留的日志关键词 - 只保留WebSocket连接状态相关的核心日志
    relevant_patterns = [
        r'connection is OPEN',
        r'connection is CLOSING', 
        r'connection is CLOSED',
        r'< PING',
        r'> PONG',
        r'< CLOSE',
        r'> CLOSE'
    ]
    
    # 编译正则表达式
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in relevant_patterns]
    
    # 收集相关行
    relevant_lines = []
    for i in range(context_start, context_end):
        line = lines[i].strip()
        
        # 检查是否包含相关关键词
        is_relevant = any(pattern.search(line) for pattern in compiled_patterns)
        
        # 检查是否是目标行（PING/PONG/连接状态的具体行）
        is_target = (i == start_line or i == end_line) and is_relevant
        
        if is_relevant:
            prefix = ">>> " if is_target else "    "
            relevant_lines.append((i, prefix, line))
    
    if relevant_lines:
        print(f"  相关连接日志 (从行 {context_start + 1} - {context_end} 中筛选):")
        print("  " + "=" * 80)
        
        for line_num, prefix, line_content in relevant_lines:
            print(f"  {prefix}{line_num + 1:4d}: {line_content}")
        
        print("  " + "=" * 80)
    else:
        print("  未找到相关的连接日志")


def main():
    parser = argparse.ArgumentParser(description='分析WebSocket PING PONG时间间隔')
    parser.add_argument('log_file', nargs='?', default='tmp/server.log', 
                       help='日志文件路径 (默认: tmp/server.log)')
    parser.add_argument('-t', '--timeout', type=float, default=3.0,
                       help='超时阈值（秒）(默认: 3.0)')
    parser.add_argument('-c', '--context', type=int, default=10,
                       help='显示上下文日志行数 (默认: 10)')
    
    args = parser.parse_args()
    
    print(f"分析日志文件: {args.log_file}")
    print(f"超时阈值: {args.timeout} 秒")
    print(f"上下文行数: {args.context}")
    print("-" * 60)
    
    # 读取日志文件
    lines = read_log_lines(args.log_file)
    print(f"总共读取 {len(lines)} 行日志")
    
    # 提取PING PONG事件
    events = extract_ping_pong_events(lines)
    ping_count = sum(1 for _, _, event_type in events if event_type == 'PING')
    pong_count = sum(1 for _, _, event_type in events if event_type == 'PONG')
    
    # 提取连接关闭事件
    closing_events = extract_closing_events(lines)
    
    # 提取连接建立事件
    open_events = extract_open_events(lines)
    
    print(f"发现 {ping_count} 个PING事件, {pong_count} 个PONG事件")
    print(f"发现 {len(closing_events)} 个连接关闭事件")
    print(f"发现 {len(open_events)} 个连接建立事件")
    
    if not events and not closing_events and not open_events:
        print("未发现PING PONG事件、连接关闭事件或连接建立事件")
        return
    
    # 分析时间间隔
    timeouts = analyze_ping_pong_intervals(events, args.timeout)
    
    # 分析连接关闭间隔
    closing_intervals = analyze_closing_intervals(events, closing_events)
    
    # 分析连接建立到第一个PING的间隔
    open_to_ping_intervals = analyze_open_to_ping_intervals(events, open_events)
    
    print(f"\n发现 {len(timeouts)} 次超过 {args.timeout} 秒的PING PONG间隔:")
    print("=" * 60)
    
    if not timeouts:
        print("✅ 所有PING PONG间隔都在正常范围内")
    else:
        for i, (ping_line, pong_line, interval, ping_time, pong_time) in enumerate(timeouts, 1):
            print(f"\n❌ 超时 #{i}: {interval:.3f} 秒")
            print(f"   PING时间: {ping_time} (行 {ping_line + 1})")
            print(f"   PONG时间: {pong_time} (行 {pong_line + 1})")
            print(f"   间隔: {interval:.3f} 秒 (超出阈值 {interval - args.timeout:.3f} 秒)")
            
            # 打印上下文日志
            print_context_logs(lines, ping_line, pong_line, args.context)
    
    # 显示连接关闭分析
    if closing_events:
        print(f"\n🔌 连接关闭分析 ({len(closing_events)} 个关闭事件):")
        print("=" * 60)
        
        if closing_intervals:
            for i, (last_event_time, last_event_type, closing_time, interval, event_line, closing_line) in enumerate(closing_intervals, 1):
                print(f"\n🔴 连接关闭 #{i}:")
                print(f"   最后{last_event_type}时间: {last_event_time} (行 {event_line + 1})")
                print(f"   连接关闭时间: {closing_time} (行 {closing_line + 1})")
                print(f"   时间间隔: {interval:.3f} 秒")
                
                if interval > args.timeout:  # 使用用户指定的超时阈值
                    print(f"   ❌ 超时！间隔超过 {args.timeout} 秒阈值 ({interval - args.timeout:.3f} 秒)")
                elif interval < 1:  # 小于1秒算正常
                    print(f"   ✅ 间隔正常")
                else:
                    print(f"   ⚠️  间隔: {interval:.3f} 秒 (在阈值内)")
                
                # 打印上下文日志
                print_context_logs(lines, event_line, closing_line, args.context)
        else:
            # 有关闭事件但没有找到对应的PING/PONG
            print(f"\n⚠️  发现 {len(closing_events)} 个连接关闭事件，但未找到对应的PING/PONG消息")
            for i, (closing_line, closing_time) in enumerate(closing_events, 1):
                print(f"\n🔴 连接关闭 #{i}:")
                print(f"   关闭时间: {closing_time} (行 {closing_line + 1})")
                print(f"   ❌ 在此关闭事件之前未找到任何PING/PONG消息")
                
                # 打印关闭事件上下文日志
                print_context_logs(lines, closing_line, closing_line, args.context)
    else:
        print(f"\n🔌 未发现连接关闭事件")
    
    # 显示连接建立到PING分析
    if open_to_ping_intervals:
        print(f"\n🚀 连接建立到第一个PING分析 ({len(open_to_ping_intervals)} 次):")
        print("=" * 60)
        
        for i, (open_time, ping_time, interval, open_line, ping_line) in enumerate(open_to_ping_intervals, 1):
            print(f"\n🟢 连接建立 #{i}:")
            print(f"   连接建立时间: {open_time} (行 {open_line + 1})")
            print(f"   第一个PING时间: {ping_time} (行 {ping_line + 1})")
            print(f"   时间间隔: {interval:.3f} 秒")
            
            if interval > args.timeout:  # 使用用户指定的超时阈值
                print(f"   ❌ 超时！间隔超过 {args.timeout} 秒阈值 ({interval - args.timeout:.3f} 秒)")
            elif interval < 1:  # 小于1秒算正常
                print(f"   ✅ 间隔正常")
            else:
                print(f"   ⚠️  间隔: {interval:.3f} 秒 (在阈值内)")
            
            # 打印上下文日志
            print_context_logs(lines, open_line, ping_line, args.context)
    elif open_events:
        print(f"\n🚀 发现 {len(open_events)} 个连接建立事件，但未找到对应的PING消息")
        for i, (open_line, open_time) in enumerate(open_events, 1):
            print(f"\n🟢 连接建立 #{i}:")
            print(f"   建立时间: {open_time} (行 {open_line + 1})")
            print(f"   ❌ 在此连接建立之后未找到任何PING消息")
            
            # 打印建立事件上下文日志
            print_context_logs(lines, open_line, open_line, args.context)
    else:
        print(f"\n🚀 未发现连接建立事件")
    
    # 统计信息
    if events:
        all_intervals = []
        i = 0
        while i < len(events) - 1:
            current_line, current_time, current_type = events[i]
            if current_type == 'PING':
                next_i = i + 1
                while next_i < len(events):
                    next_line, next_time, next_type = events[next_i]
                    if next_type == 'PONG':
                        interval = (next_time - current_time).total_seconds()
                        all_intervals.append(interval)
                        break
                    next_i += 1
            i += 1
        
        if all_intervals:
            avg_interval = sum(all_intervals) / len(all_intervals)
            max_interval = max(all_intervals)
            min_interval = min(all_intervals)
            
            print(f"\n📊 统计信息:")
            # 计算各种超时次数
            closing_timeouts = sum(1 for _, _, _, interval, _, _ in closing_intervals if interval > args.timeout)
            open_to_ping_timeouts = sum(1 for _, _, interval, _, _ in open_to_ping_intervals if interval > args.timeout)
            total_timeouts = len(timeouts) + closing_timeouts + open_to_ping_timeouts
            
            print(f"   总PING PONG对数: {len(all_intervals)}")
            print(f"   平均间隔: {avg_interval:.3f} 秒")
            print(f"   最大间隔: {max_interval:.3f} 秒")
            print(f"   最小间隔: {min_interval:.3f} 秒")
            print(f"   PING PONG超时次数: {len(timeouts)}")
            if len(all_intervals) > 0:
                print(f"   PING PONG超时率: {len(timeouts)/len(all_intervals)*100:.1f}%")
            print(f"   连接关闭次数: {len(closing_events)}")
            print(f"   连接关闭超时次数: {closing_timeouts}")
            print(f"   连接建立次数: {len(open_events)}")
            print(f"   连接建立到PING超时次数: {open_to_ping_timeouts}")
            print(f"   总超时次数: {total_timeouts}")
            
            if closing_intervals:
                avg_closing_interval = sum(interval for _, _, _, interval, _, _ in closing_intervals) / len(closing_intervals)
                max_closing_interval = max(interval for _, _, _, interval, _, _ in closing_intervals)
                min_closing_interval = min(interval for _, _, _, interval, _, _ in closing_intervals)
                print(f"   平均关闭间隔: {avg_closing_interval:.3f} 秒")
                print(f"   最大关闭间隔: {max_closing_interval:.3f} 秒")
                print(f"   最小关闭间隔: {min_closing_interval:.3f} 秒")
            
            if open_to_ping_intervals:
                avg_open_interval = sum(interval for _, _, interval, _, _ in open_to_ping_intervals) / len(open_to_ping_intervals)
                max_open_interval = max(interval for _, _, interval, _, _ in open_to_ping_intervals)
                min_open_interval = min(interval for _, _, interval, _, _ in open_to_ping_intervals)
                print(f"   平均建立到PING间隔: {avg_open_interval:.3f} 秒")
                print(f"   最大建立到PING间隔: {max_open_interval:.3f} 秒")
                print(f"   最小建立到PING间隔: {min_open_interval:.3f} 秒")


if __name__ == "__main__":
    main()