#!/usr/bin/env python3
"""
批量音频文件ASR测试脚本
输入命令行参数如 python scan_files.py test_dir/asr*.wav output.csv
本脚本会扫描指定目录下的所有wav文件，参考 core/providers/asr/fun_local.py 中的代码，
将wav文件转换为文本，并保存到output.csv文件中。

使用方法:
    python scan_files.py "test_dir/*.wav" output.csv
    python scan_files.py test_dir/audio1.wav output.csv
    python scan_files.py test_dir/ output.csv  # 扫描整个目录
"""

import sys
import os
import glob
import csv
import asyncio
import wave
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from config.config_loader import load_config
from core.utils.util import initialize_modules
from config.logger import setup_logging


def check_working_directory():
    """
    检查当前工作目录是否为项目根目录
    如果不是，则报错退出
    """
    current_dir = os.getcwd()
    
    # 检查当前目录是否包含项目的关键文件
    required_files = ['config.yaml', 'requirements.txt']
    required_dirs = ['core', 'config', 'test']
    
    missing_items = []
    
    # 检查必需的文件
    for file_name in required_files:
        if not os.path.isfile(os.path.join(current_dir, file_name)):
            missing_items.append(f"文件: {file_name}")
    
    # 检查必需的目录
    for dir_name in required_dirs:
        if not os.path.isdir(os.path.join(current_dir, dir_name)):
            missing_items.append(f"目录: {dir_name}")
    
    if missing_items:
        print("❌ 错误：请在项目根目录下运行此脚本！")
        print(f"当前工作目录: {current_dir}")
        print(f"项目根目录应该是: {project_root}")
        print(f"缺少以下项目文件/目录: {', '.join(missing_items)}")
        print("")
        print("正确的运行方式:")
        print("  cd ~/Projects/yuyan-server")
        print("  python test/asr_test/scan_files.py <参数>")
        print("")
        print("或者使用绝对路径:")
        print(f"  cd {project_root}")
        print("  python test/asr_test/scan_files.py <参数>")
        sys.exit(1)
    
    print(f"✅ 工作目录检查通过: {current_dir}")
    return True


def print_usage():
    """打印使用说明"""
    print("使用方法:")
    print("  方式1: python scan_files.py <音频文件路径/模式> <输出CSV文件>")
    print("  方式2: python scan_files.py <文件1> <文件2> ... <输出CSV文件>")
    print("")
    print("示例:")
    print("  # 使用通配符模式（需要加引号）")
    print("  python scan_files.py \"test_dir/*.wav\" output.csv")
    print("  python scan_files.py \"~/tmp/asr_*.wav\" output.csv")
    print("")
    print("  # 指定单个文件或目录")
    print("  python scan_files.py test_dir/audio1.wav output.csv")
    print("  python scan_files.py test_dir/ output.csv")
    print("")
    print("  # 让shell展开通配符（不加引号）")
    print("  python scan_files.py ~/tmp/asr_*.wav output.csv")
    print("  python scan_files.py file1.wav file2.wav file3.wav output.csv")
    print("")
    print("支持的音频格式: .wav")


def find_audio_files(path_pattern):
    """
    根据路径模式查找音频文件
    
    Args:
        path_pattern: 文件路径或模式，支持通配符
        
    Returns:
        list: 音频文件路径列表
    """
    audio_files = []
    
    # 展开用户目录 (~)
    expanded_pattern = os.path.expanduser(path_pattern)
    
    # 如果是目录，扫描目录下的所有wav文件
    if os.path.isdir(expanded_pattern):
        pattern = os.path.join(expanded_pattern, "*.wav")
        audio_files = glob.glob(pattern)
    # 如果包含通配符，使用glob匹配
    elif "*" in expanded_pattern or "?" in expanded_pattern:
        audio_files = glob.glob(expanded_pattern)
    # 如果是单个文件
    elif os.path.isfile(expanded_pattern):
        audio_files = [expanded_pattern]
    
    # 过滤出wav文件
    audio_files = [f for f in audio_files if f.lower().endswith('.wav')]
    
    return sorted(audio_files)


def read_wav_file(file_path):
    """
    读取WAV文件并转换为PCM数据
    
    Args:
        file_path: WAV文件路径
        
    Returns:
        bytes: PCM数据
    """
    try:
        with wave.open(file_path, 'rb') as wf:
            # 检查音频格式
            channels = wf.getnchannels()
            sample_width = wf.getsampwidth()
            framerate = wf.getframerate()
            
            print(f"  音频格式: {channels}声道, {sample_width*8}bit, {framerate}Hz")
            
            # 读取所有帧
            frames = wf.readframes(wf.getnframes())
            return frames
            
    except Exception as e:
        print(f"  ❌ 读取WAV文件失败: {e}")
        return None


async def process_audio_file(asr_provider, file_path, session_id="batch_test"):
    """
    处理单个音频文件
    
    Args:
        asr_provider: ASR提供商实例
        file_path: 音频文件路径
        session_id: 会话ID
        
    Returns:
        tuple: (文件路径, 识别文本, 处理时间, 错误信息)
    """
    print(f"🎵 处理文件: {file_path}")
    
    try:
        start_time = time.time()
        
        # 读取WAV文件
        pcm_data = read_wav_file(file_path)
        if pcm_data is None:
            return file_path, "", 0, "读取文件失败"
        
        # 设置音频格式为PCM
        asr_provider.set_audio_format("pcm")
        
        # 调用ASR进行语音识别
        text, _ = await asr_provider.speech_to_text([pcm_data], session_id)
        
        process_time = time.time() - start_time
        
        if text:
            print(f"  ✅ 识别结果: {text}")
            print(f"  ⏱️  处理时间: {process_time:.3f}秒")
            return file_path, text, process_time, ""
        else:
            print(f"  ❌ 识别失败: 无结果")
            return file_path, "", process_time, "识别无结果"
            
    except Exception as e:
        process_time = time.time() - start_time if 'start_time' in locals() else 0
        error_msg = str(e)
        print(f"  ❌ 处理失败: {error_msg}")
        return file_path, "", process_time, error_msg


def save_results_to_csv(results, output_file):
    """
    保存结果到CSV文件
    
    Args:
        results: 结果列表
        output_file: 输出CSV文件路径
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(['文件路径', '识别文本', '处理时间(秒)', '错误信息'])
            
            # 写入数据
            for result in results:
                writer.writerow(result)
                
        print(f"✅ 结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存CSV文件失败: {e}")


async def main():
    """主函数"""
    # 首先检查工作目录
    check_working_directory()
    
    # 检查命令行参数
    if len(sys.argv) < 3:
        print("❌ 参数错误!")
        print_usage()
        sys.exit(1)
    
    # 如果有多个参数，说明shell已经展开了通配符
    # 最后一个参数是输出文件，前面的都是音频文件
    if len(sys.argv) == 3:
        # 标准用法：python scan_files.py "pattern" output.csv
        input_pattern = sys.argv[1]
        output_file = sys.argv[2]
        audio_files = find_audio_files(input_pattern)
    else:
        # 展开用法：python scan_files.py file1.wav file2.wav ... output.csv
        audio_files = sys.argv[1:-1]  # 除了最后一个参数外的所有参数
        output_file = sys.argv[-1]    # 最后一个参数是输出文件
        
        # 过滤出wav文件并验证文件存在
        valid_files = []
        for file_path in audio_files:
            if file_path.lower().endswith('.wav') and os.path.isfile(file_path):
                valid_files.append(file_path)
            elif not file_path.lower().endswith('.wav'):
                print(f"⚠️  跳过非WAV文件: {file_path}")
            elif not os.path.isfile(file_path):
                print(f"⚠️  文件不存在: {file_path}")
        
        audio_files = sorted(valid_files)
    
    print("🔧 批量音频文件ASR测试工具")
    print("=" * 50)
    
    # 显示搜索信息
    if len(sys.argv) == 3:
        print(f"📁 搜索音频文件: {input_pattern}")
    else:
        print(f"📁 处理指定的 {len(sys.argv)-2} 个文件")
    
    if not audio_files:
        print("❌ 未找到任何音频文件!")
        print_usage()
        sys.exit(1)
    
    print(f"📋 找到 {len(audio_files)} 个音频文件:")
    for i, file_path in enumerate(audio_files, 1):
        print(f"  {i}. {file_path}")
    
    print("\n🚀 开始初始化ASR模块...")
    
    try:
        # 加载配置
        config = load_config()
        logger = setup_logging()
        
        # 初始化ASR模块
        modules = initialize_modules(
            logger,
            config,
            None,  # role_loader (not needed for ASR testing)
            init_vad=False,
            init_asr=True,   # Only initialize ASR for testing
            init_llm=False,
            init_tts=False,
            init_memory=False,
            init_intent=False,
        )
        
        if "asr" not in modules:
            print("❌ ASR模块初始化失败!")
            sys.exit(1)
        
        asr_provider = modules["asr"]
        print("✅ ASR模块初始化成功!")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n🎯 开始批量处理音频文件...")
    print("=" * 50)
    
    # 批量处理音频文件
    results = []
    total_files = len(audio_files)
    
    for i, file_path in enumerate(audio_files, 1):
        print(f"\n[{i}/{total_files}] ", end="")
        result = await process_audio_file(asr_provider, file_path)
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 处理完成，统计结果:")
    
    # 统计结果
    success_count = sum(1 for r in results if r[1] and not r[3])
    failed_count = total_files - success_count
    total_time = sum(r[2] for r in results)
    avg_time = total_time / total_files if total_files > 0 else 0
    
    print(f"  总文件数: {total_files}")
    print(f"  成功识别: {success_count}")
    print(f"  识别失败: {failed_count}")
    print(f"  总处理时间: {total_time:.3f}秒")
    print(f"  平均处理时间: {avg_time:.3f}秒/文件")
    
    # 保存结果到CSV
    print(f"\n💾 保存结果到: {output_file}")
    save_results_to_csv(results, output_file)
    
    # 显示失败的文件
    if failed_count > 0:
        print(f"\n❌ 失败的文件 ({failed_count}个):")
        for result in results:
            if result[3]:  # 有错误信息
                print(f"  {result[0]}: {result[3]}")
    
    print("\n🎉 批量处理完成!")


if __name__ == "__main__":
    asyncio.run(main())