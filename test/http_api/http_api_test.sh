#!/bin/bash

# 测试本项目 HTTP RESTful API 接口的脚本

#API_BASE_URL="http://10.11.50.31:8100"
API_BASE_URL="https://chat-api-yuyan.zzfx.net.cn"

# 认证信息 - 基于实际日志中的Header格式
CLIENT_ID="27u5ct86k111001mb55p2rw1lbqm0364"  # 实际client-id格式
DEVICE_ID="7f:83:ab:82:93:f9"  # 实际device-id格式（MAC地址）
AUTH_TOKEN="your-token1"  # 请确保这个token在config.yaml的server.auth.tokens中配置

# 如果使用APUS账号系统认证，可以参考以下复杂token格式：
# AUTH_TOKEN="psu=xKxUS8eya3l240718%2BYti%2FUdJ%2FX8XEX8vHWMW7ITIjntpcGkmPaEBKjcfXbhZAvbL58caI6GkhgoUzIhZexloD%2FcBB9weupdQHbYWM3LyTLOkwO3UXfQJ%2B%2FVXPaMG7OnJwxX9j1%2BP%2F9NVJbc%2B8mHkxaivn5iINxVm0iWCdEFxGA%3D;pmc=PNOkzKA2APSVsdGqXVdTWB8S%2B2jJsa6gtuBnFjBBuKKDQ%2BYgRGncGlZH%2BPL7duNj5yBkIictwQHNufYXX1epPuPoO5sNKgqww0Y1fsKou3xNX789ce2PEp2lk3YDiJ9VyTPKvOEuYwUUYSEvSJ09RqvZLdxJBKIuG2CO45LGuA4%3D"

echo "========================================"
echo "YuYan HTTP API 快速测试"
echo "========================================"
echo "API服务器地址: $API_BASE_URL"
echo "客户端ID: $CLIENT_ID"
echo "设备ID: $DEVICE_ID"
echo "认证令牌: $AUTH_TOKEN"
echo ""

# 测试1：设置用户档案接口（包含AI性别和名字）
echo "测试1: 设置用户档案接口 - 女性AI助手小美"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "user_name": "小明",
    "lifestyle": "健康生活，喜欢早起锻炼，注重工作与生活平衡",
    "interests": "阅读、跑步、编程、摄影、旅行",
    "ai_gender": "女",
    "ai_name": "小美",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试2：文本摘要接口
echo "测试2: 文本摘要接口"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/text_summary" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "text": "今天下午2点，我们团队在会议室召开了项目进度讨论会。参与人员包括：张经理、李工程师、王设计师、陈测试员。会议主要讨论了项目进度、遇到的问题和解决方案。决议：李工程师负责数据库优化，王设计师负责UI调整，陈测试员准备测试用例。下次会议时间：下周五下午2点。"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试3：设置男性AI助手
echo "测试3: 设置用户档案接口 - 男性AI助手小杰"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "user_name": "王小红",
    "lifestyle": "喜欢安静，热爱学习和思考",
    "interests": "绘画、音乐、编程、哲学",
    "ai_gender": "男",
    "ai_name": "小杰",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试4：只设置AI名字，不设置性别
echo "测试4: 设置用户档案接口 - 只设置AI名字（小智）"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "user_name": "张三",
    "lifestyle": "工作忙碌，追求效率",
    "interests": "科技、投资、健身",
    "ai_name": "小智",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试5：只设置性别，不设置名字
echo "测试5: 设置用户档案接口 - 只设置性别（女性）"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "user_name": "赵丽",
    "lifestyle": "追求品质生活，注重美学",
    "interests": "美食、时尚、摄影、艺术",
    "ai_gender": "女",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试6：不设置AI性别和名字（原有功能）
echo "测试6: 设置用户档案接口 - 不设置AI性别和名字（原有功能）"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d '{
    "user_name": "孙悟空",
    "lifestyle": "自由自在，无拘无束",
    "interests": "武术、冒险、保护朋友",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试7：认证失败示例
echo "测试3: 认证失败示例（使用错误token）"
echo "----------------------------------------"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer invalid-token" \
  -d '{
    "user_name": "测试用户",
    "lifestyle": "测试",
    "interests": "测试",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"

echo ""
echo "========================================"
echo ""

# 测试8: 使用真实APUS token格式（如果配置了account认证）
echo "测试8: 使用真实APUS token格式（如果配置了account认证）"
echo "----------------------------------------"
# 取消注释下面的行来测试APUS账号系统认证
: <<'COMMENT'
APUS_TOKEN="psu=xKxUS8eya3l240718%2BYti%2FUdJ%2FX8XEX8vHWMW7ITIjntpcGkmPaEBKjcfXbhZAvbL58caI6GkhgoUzIhZexloD%2FcBB9weupdQHbYWM3LyTLOkwO3UXfQJ%2B%2FVXPaMG7OnJwxX9j1%2BP%2F9NVJbc%2B8mHkxaivn5iINxVm0iWCdEFxGA%3D;pmc=PNOkzKA2APSVsdGqXVdTWB8S%2B2jJsa6gtuBnFjBBuKKDQ%2BYgRGncGlZH%2BPL7duNj5yBkIictwQHNufYXX1epPuPoO5sNKgqww0Y1fsKou3xNX789ce2PEp2lk3YDiJ9VyTPKvOEuYwUUYSEvSJ09RqvZLdxJBKIuG2CO45LGuA4%3D"
curl -X POST "$API_BASE_URL/api/v1/set_user_profile" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $APUS_TOKEN" \
  -d '{
    "user_name": "APUS用户",
    "lifestyle": "移动互联网用户",
    "interests": "科技、应用",
    "client_id": "'"$CLIENT_ID"'"
  }' \
  -w "\n状态码: %{http_code}\n"
COMMENT

echo "（APUS token测试已注释，如需测试请取消注释）"

echo ""
echo "========================================"
echo "快速测试完成！"
echo ""
echo "📋 使用说明："
echo "1. 【重要】确保在 config.yaml 中配置了正确的认证信息："
echo "   server.auth.enabled: true"
echo "   server.auth.auth_type: \"static\" 或 \"account\""
echo ""
echo "2. 静态token认证(auth_type: static)："
echo "   - 修改 AUTH_TOKEN 为 config.yaml 中 server.auth.tokens 里的token"
echo "   - 示例：AUTH_TOKEN=\"your-token1\""
echo ""
echo "3. APUS账号认证(auth_type: account)："
echo "   - 取消注释测试8中的APUS token格式"
echo "   - 使用实际的APUS认证token"
echo ""
echo "4. Header格式参考："
echo "   - client-id: 32位随机字符串（如：27u5ct86k111001mb55p2rw1lbqm0364）"
echo "   - device-id: MAC地址格式（如：7f:83:ab:82:93:f9）"
echo "   - authorization: Bearer <token>"
echo ""
echo "5. AI性别和名字字段说明："
echo "   - ai_gender: 用户希望的AI性别（男/女/male/female/男性/女性）"
echo "   - ai_name: 用户希望给AI起的名字（任意字符串）"
echo "   - 这两个字段都是可选的，可以只设置其中一个或都不设置"
echo "   - 设置后会影响个性化问候语的生成和用户数据存储"
echo ""
echo "6. AI角色映射规则："
echo "   - 男性AI（男/male/男性） → 治愈男闺蜜角色"
echo "   - 女性AI（女/female/女性） → 俏皮女闺蜜角色"
echo "   - 角色信息会自动保存到用户档案中"
echo ""
echo "7. 预期状态码："
echo "   - 成功: 200"
echo "   - 认证失败: 401"
echo "   - 服务器错误: 500"
echo ""
echo "8. 测试覆盖场景："
echo "   - 测试1: 设置女性AI助手（小美 → 俏皮女闺蜜角色）"
echo "   - 测试2: 文本摘要功能"
echo "   - 测试3: 设置男性AI助手（小杰 → 治愈男闺蜜角色）"
echo "   - 测试4: 只设置AI名字（小智）"
echo "   - 测试5: 只设置AI性别（女性 → 俏皮女闺蜜角色）"
echo "   - 测试6: 不设置AI信息（原有功能）"
echo "   - 测试7: 认证失败示例"
echo "   - 测试8: APUS token格式（可选）"
echo ""
echo "9. 响应字段说明："
echo "   - ai_role: 根据ai_gender自动映射的角色名称"
echo "   - 角色信息会保存到Redis的role和assistant_name字段"
echo "========================================"
