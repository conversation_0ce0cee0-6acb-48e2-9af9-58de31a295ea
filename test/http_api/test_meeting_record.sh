#!/bin/bash

# 测试 YuYan HTTP API 音频会议记录接口的脚本
# 包含音频摘要上传和任务状态查询功能

API_BASE_URL="http://192.168.220.166:8100"

# 认证信息 - 基于实际日志中的Header格式
CLIENT_ID="27u5ct86k111001mb55p2rw1lbqm0364"  # 实际client-id格式
DEVICE_ID="7f:83:ab:82:93:f9"  # 实际device-id格式（MAC地址）
AUTH_TOKEN="your-token1"  # 请确保这个token在config.yaml的server.auth.tokens中配置

# 全局变量用于存储任务ID
GLOBAL_TASK_ID=""

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 [MP3文件路径] [会议纪要文件路径]"
    echo ""
    echo "参数说明:"
    echo "  MP3文件路径      - 可选，要上传的MP3音频文件路径"
    echo "  会议纪要文件路径  - 可选，之前的会议纪要文本文件路径"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认的测试文件"
    echo "  $0 meeting.mp3                        # 指定MP3文件"
    echo "  $0 meeting.mp3 previous_summary.txt   # 指定MP3文件和会议纪要文件"
    echo ""
}

# 处理命令行参数
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# 设置文件路径
TEST_MP3_PATH="${1:-test_meeting.mp3}"
TEST_SUMMARY_PATH="${2:-previous_summary.txt}"

echo "========================================"
echo "YuYan HTTP API 音频会议记录接口测试"
echo "========================================"
echo "API服务器地址: $API_BASE_URL"
echo "客户端ID: $CLIENT_ID"
echo "设备ID: $DEVICE_ID"
echo "认证令牌: $AUTH_TOKEN"
echo "MP3文件: $TEST_MP3_PATH"
echo "会议纪要文件: $TEST_SUMMARY_PATH"
echo ""

# 创建默认测试文件（如果不存在且使用默认路径）
create_default_test_files() {
    # 只有在使用默认路径且文件不存在时才创建
    if [ "$TEST_MP3_PATH" = "test_meeting.mp3" ] && [ ! -f "$TEST_MP3_PATH" ]; then
        echo "⚠️  创建默认MP3测试文件: $TEST_MP3_PATH"
        echo "这是一个模拟的MP3文件用于测试" > "$TEST_MP3_PATH"
        echo "注意：实际使用时请替换为真实的MP3音频文件"
        echo ""
    fi
    
    if [ "$TEST_SUMMARY_PATH" = "previous_summary.txt" ] && [ ! -f "$TEST_SUMMARY_PATH" ]; then
        echo "📝 创建默认会议纪要测试文件: $TEST_SUMMARY_PATH"
        cat > "$TEST_SUMMARY_PATH" << 'EOF'
# 会议纪要（第一部分）

## 基本信息
- 会议时间：2024年06月26日 14:30
- 会议主题：产品功能讨论

## 主要讨论内容
1. 讨论了用户界面优化方案
2. 确定了下一阶段的开发重点

## 决议事项
1. 采用新的UI设计方案
2. 增加音频处理功能

## 行动计划
1. UI团队下周完成设计稿
2. 开发团队开始音频模块开发

## 其他事项
无
EOF
        echo "已创建默认会议纪要测试文件"
        echo ""
    fi
}

# 函数：执行HTTP请求并格式化输出
test_api() {
    local test_name="$1"
    local curl_cmd="$2"
    
    echo "测试: $test_name"
    echo "----------------------------------------"
    
    # 执行curl命令并保存响应
    echo "发送请求..."
    RESPONSE=$(eval "$curl_cmd" 2>&1)
    EXIT_CODE=$?
    
    echo ""
    echo "原始响应:"
    echo "$RESPONSE"
    echo ""
    
    # 提取HTTP状态码
    HTTP_CODE=$(echo "$RESPONSE" | grep -E '^状态码: [0-9]+$' | sed 's/状态码: //')
    
    # 检查HTTP状态码
    if [ -n "$HTTP_CODE" ]; then
        echo "HTTP状态码: $HTTP_CODE"
        if [ "$HTTP_CODE" != "200" ]; then
            echo "❌ 错误: HTTP状态码不是200 (实际: $HTTP_CODE)"
            echo "❌ 测试失败: $test_name"
        else
            echo "✅ HTTP状态码检查通过"
        fi
    else
        echo "⚠️  警告: 无法提取HTTP状态码"
    fi
    
    # 尝试提取JSON部分并格式化
    JSON_PART=$(echo "$RESPONSE" | grep -E '^{.*}$' | head -1)
    if [ -n "$JSON_PART" ]; then
        echo "格式化JSON:"
        echo "$JSON_PART" | jq '.' 2>/dev/null || echo "JSON格式化失败"
        
        # 提取任务ID（如果存在）
        TASK_ID=$(echo "$JSON_PART" | jq -r '.data.task_id // empty' 2>/dev/null)
        if [ -n "$TASK_ID" ] && [ "$TASK_ID" != "null" ]; then
            echo ""
            echo "🆔 提取到任务ID: $TASK_ID"
            # 将任务ID保存到全局变量
            GLOBAL_TASK_ID="$TASK_ID"
        fi
    else
        echo "未找到有效的JSON响应"
    fi
    
    echo ""
    echo "========================================"
    echo ""
}

# 函数：查询任务状态
query_task_status() {
    local task_id="$1"
    
    if [ -z "$task_id" ]; then
        echo "❌ 错误: 任务ID为空，无法查询状态"
        return 1
    fi
    
    test_api "查询音频摘要任务状态 - 任务ID: $task_id" \
    "curl -X GET \"$API_BASE_URL/api/v1/audio_summary/$task_id\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 函数：等待用户确认
wait_for_user() {
    echo ""
    echo "按 Enter 键返回主菜单，或输入 'q' 退出..."
    read -r user_input
    if [ "$user_input" = "q" ] || [ "$user_input" = "Q" ]; then
        echo "退出测试脚本。"
        exit 0
    fi
}

# 测试函数1：音频摘要上传 - 包含之前的会议纪要
test_audio_upload_with_summary() {
    if [ ! -f "$TEST_MP3_PATH" ]; then
        echo "❌ MP3文件不存在: $TEST_MP3_PATH"
        return 1
    fi
    
    if [ ! -f "$TEST_SUMMARY_PATH" ]; then
        echo "❌ 会议纪要文件不存在: $TEST_SUMMARY_PATH"
        return 1
    fi
    
    echo "🎵 开始测试音频摘要上传（包含之前的会议纪要）..."
    test_api "音频摘要上传 - 带之前的会议纪要" \
    "curl -X POST \"$API_BASE_URL/api/v1/audio_summary\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -F \"audio=@$TEST_MP3_PATH\" \
      -F \"text=<$TEST_SUMMARY_PATH\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 测试函数2：音频摘要上传 - 首次上传
test_audio_upload_first_time() {
    if [ ! -f "$TEST_MP3_PATH" ]; then
        echo "❌ MP3文件不存在: $TEST_MP3_PATH"
        return 1
    fi
    
    echo "🎵 开始测试音频摘要上传（首次上传，无之前纪要）..."
    test_api "音频摘要上传 - 首次上传" \
    "curl -X POST \"$API_BASE_URL/api/v1/audio_summary\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -F \"audio=@$TEST_MP3_PATH\" \
      -F \"text=\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 测试函数3：查询任务状态
test_query_task_status() {
    if [ -z "$GLOBAL_TASK_ID" ]; then
        echo "请先输入任务ID，或者先执行音频上传测试获取任务ID："
        echo -n "任务ID: "
        read -r task_id
        if [ -z "$task_id" ]; then
            echo "❌ 任务ID不能为空"
            return 1
        fi
        GLOBAL_TASK_ID="$task_id"
    fi
    
    echo "🔍 查询任务状态..."
    query_task_status "$GLOBAL_TASK_ID"
}

# 测试函数4：等待并多次查询任务状态
test_wait_and_query() {
    if [ -z "$GLOBAL_TASK_ID" ]; then
        echo "请先输入任务ID，或者先执行音频上传测试获取任务ID："
        echo -n "任务ID: "
        read -r task_id
        if [ -z "$task_id" ]; then
            echo "❌ 任务ID不能为空"
            return 1
        fi
        GLOBAL_TASK_ID="$task_id"
    fi
    
    echo "⏳ 等待5秒让任务开始处理..."
    sleep 5
    
    echo "🔍 第1次查询任务状态..."
    query_task_status "$GLOBAL_TASK_ID"
    
    echo "⏳ 等待10秒让任务继续处理..."
    sleep 10
    
    echo "🔍 第2次查询任务状态（检查是否完成）..."
    query_task_status "$GLOBAL_TASK_ID"
}

# 测试函数5：查询不存在的任务ID
test_query_nonexistent_task() {
    echo "🔍 测试查询不存在的任务ID..."
    test_api "查询不存在的任务ID" \
    "curl -X GET \"$API_BASE_URL/api/v1/audio_summary/nonexistent-task-id\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 测试函数6：错误的文件类型
test_invalid_file_type() {
    echo "📝 创建临时文本文件用于测试..."
    echo "这是一个文本文件，不是MP3" > /tmp/test_invalid_audio.txt
    
    test_api "音频摘要上传 - 错误的文件类型（应该失败）" \
    "curl -X POST \"$API_BASE_URL/api/v1/audio_summary\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -F \"audio=@/tmp/test_invalid_audio.txt\" \
      -F \"text=\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
    
    rm -f /tmp/test_invalid_audio.txt
}

# 测试函数7：没有文件
test_no_file() {
    test_api "音频摘要上传 - 没有文件（应该失败）" \
    "curl -X POST \"$API_BASE_URL/api/v1/audio_summary\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer $AUTH_TOKEN\" \
      -F \"invalid_field=test\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 测试函数8：认证失败 - 音频上传
test_auth_fail_upload() {
    if [ ! -f "$TEST_MP3_PATH" ]; then
        echo "❌ MP3文件不存在: $TEST_MP3_PATH"
        return 1
    fi
    
    test_api "认证失败 - 音频上传（使用错误token）" \
    "curl -X POST \"$API_BASE_URL/api/v1/audio_summary\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer invalid-token\" \
      -F \"audio=@$TEST_MP3_PATH\" \
      -F \"text=\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 测试函数9：认证失败 - 任务查询
test_auth_fail_query() {
    test_api "认证失败 - 任务查询（使用错误token）" \
    "curl -X GET \"$API_BASE_URL/api/v1/audio_summary/test-task-id\" \
      -H \"client-id: $CLIENT_ID\" \
      -H \"device-id: $DEVICE_ID\" \
      -H \"authorization: Bearer invalid-token\" \
      -w \"\\n状态码: %{http_code}\\n\" \
      -v"
}

# 显示主菜单
show_menu() {
    clear
    echo "========================================"
    echo "YuYan HTTP API 音频会议记录接口测试"
    echo "========================================"
    echo "API服务器: $API_BASE_URL"
    echo "MP3文件: $TEST_MP3_PATH"
    echo "会议纪要文件: $TEST_SUMMARY_PATH"
    if [ -n "$GLOBAL_TASK_ID" ]; then
        echo "当前任务ID: $GLOBAL_TASK_ID"
    fi
    echo ""
    echo "请选择要执行的测试："
    echo ""
    echo "=== 核心功能测试 ==="
    echo "1. 音频摘要上传（包含之前的会议纪要）"
    echo "2. 音频摘要上传（首次上传，无之前纪要）"
    echo "3. 查询任务状态"
    echo "4. 等待并多次查询任务状态"
    echo ""
    echo "=== 错误场景测试 ==="
    echo "5. 查询不存在的任务ID"
    echo "6. 上传错误的文件类型"
    echo "7. 上传请求不包含文件"
    echo "8. 认证失败 - 音频上传"
    echo "9. 认证失败 - 任务查询"
    echo ""
    echo "=== 其他操作 ==="
    echo "h. 显示帮助信息"
    echo "q. 退出"
    echo ""
    echo -n "请输入选项 (1-9, h, q): "
}

# 显示帮助信息
show_help() {
    echo ""
    echo "📋 测试说明："
    echo ""
    echo "=== 核心功能测试 ==="
    echo "1. 上传MP3文件和之前的会议纪要，返回任务ID"
    echo "2. 上传MP3文件（首次上传），返回任务ID"
    echo "3. 使用任务ID查询处理状态和结果"
    echo "4. 自动等待并多次查询，查看任务完成情况"
    echo ""
    echo "=== 错误场景测试 ==="
    echo "5-9. 测试各种错误情况的处理"
    echo ""
    echo "📋 HTTP状态码说明："
    echo "- 200: 成功"
    echo "- 400: 请求错误（如文件格式错误等）"
    echo "- 401: 认证失败"
    echo "- 500: 服务器错误"
    echo ""
    echo "🔧 如果需要查看详细的服务器日志："
    echo "tail -f tmp/server.log"
    echo ""
    echo "💡 文件要求："
    echo "- MP3文件: 真实的MP3音频文件"
    echo "- 会议纪要文件: 纯文本格式的之前会议记录"
    echo ""
}

# 创建默认测试文件
create_default_test_files

# 检查文件是否存在
echo "检查测试文件..."
if [ -f "$TEST_MP3_PATH" ]; then
    echo "✅ 找到MP3文件: $TEST_MP3_PATH"
else
    echo "❌ MP3文件不存在: $TEST_MP3_PATH"
fi

if [ -f "$TEST_SUMMARY_PATH" ]; then
    echo "✅ 找到会议纪要文件: $TEST_SUMMARY_PATH"
else
    echo "❌ 会议纪要文件不存在: $TEST_SUMMARY_PATH"
fi

echo ""
echo "按 Enter 键进入测试菜单..."
read -r

# 主循环
while true; do
    show_menu
    read -r choice
    
    case $choice in
        1)
            clear
            test_audio_upload_with_summary
            wait_for_user
            ;;
        2)
            clear
            test_audio_upload_first_time
            wait_for_user
            ;;
        3)
            clear
            test_query_task_status
            wait_for_user
            ;;
        4)
            clear
            test_wait_and_query
            wait_for_user
            ;;
        5)
            clear
            test_query_nonexistent_task
            wait_for_user
            ;;
        6)
            clear
            test_invalid_file_type
            wait_for_user
            ;;
        7)
            clear
            test_no_file
            wait_for_user
            ;;
        8)
            clear
            test_auth_fail_upload
            wait_for_user
            ;;
        9)
            clear
            test_auth_fail_query
            wait_for_user
            ;;
        h|H)
            clear
            show_help
            wait_for_user
            ;;
        q|Q)
            echo "退出测试脚本。"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            sleep 2
            ;;
    esac
done