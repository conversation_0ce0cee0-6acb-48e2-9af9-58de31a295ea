#!/bin/bash

# 测试 YuYan HTTP API 上传接口的脚本
# 包含头像上传和昵称上传功能

API_BASE_URL="http://10.11.50.31:8100"

# 认证信息 - 基于实际日志中的Header格式
CLIENT_ID="27u5ct86k111001mb55p2rw1lbqm0364"  # 实际client-id格式
DEVICE_ID="7f:83:ab:82:93:f9"  # 实际device-id格式（MAC地址）
AUTH_TOKEN="your-token1"  # 请确保这个token在config.yaml的server.auth.tokens中配置

# 测试图片路径（请根据实际情况修改）
TEST_AVATAR_PATH="../../core/unit_test/policy_check/avatars/test01.jpeg"

echo "========================================"
echo "YuYan HTTP API 上传接口测试"
echo "========================================"
echo "API服务器地址: $API_BASE_URL"
echo "客户端ID: $CLIENT_ID"
echo "设备ID: $DEVICE_ID"
echo "认证令牌: $AUTH_TOKEN"
echo ""

# 检查测试图片是否存在
if [ ! -f "$TEST_AVATAR_PATH" ]; then
    echo "⚠️  警告: 测试图片不存在: $TEST_AVATAR_PATH"
    echo "请准备一个测试图片文件，或修改脚本中的 TEST_AVATAR_PATH 变量"
    echo ""
fi

# 函数：执行HTTP请求并格式化输出
test_api() {
    local test_name="$1"
    local curl_cmd="$2"
    
    echo "测试: $test_name"
    echo "----------------------------------------"
    
    # 执行curl命令并保存响应
    echo "发送请求..."
    RESPONSE=$(eval "$curl_cmd" 2>&1)
    EXIT_CODE=$?
    
    echo ""
    echo "原始响应:"
    echo "$RESPONSE"
    echo ""
    
    # 提取HTTP状态码
    HTTP_CODE=$(echo "$RESPONSE" | grep -E '^状态码: [0-9]+$' | sed 's/状态码: //')
    
    # 检查HTTP状态码
    if [ -n "$HTTP_CODE" ]; then
        echo "HTTP状态码: $HTTP_CODE"
        if [ "$HTTP_CODE" != "200" ]; then
            echo "❌ 错误: HTTP状态码不是200 (实际: $HTTP_CODE)"
            echo "❌ 测试失败: $test_name"
            echo ""
            echo "========================================"
            echo ""
            return 1
        else
            echo "✅ HTTP状态码检查通过"
        fi
    else
        echo "⚠️  警告: 无法提取HTTP状态码"
    fi
    
    # 尝试提取JSON部分并格式化
    JSON_PART=$(echo "$RESPONSE" | grep -E '^\{.*\}$' | head -1)
    if [ -n "$JSON_PART" ]; then
        echo "格式化JSON:"
        echo "$JSON_PART" | jq '.' 2>/dev/null || echo "JSON格式化失败"
    else
        echo "未找到有效的JSON响应"
    fi
    
    echo ""
    echo "========================================"
    echo ""
}

# 测试1：昵称上传接口 - 正常昵称
test_api "昵称上传 - 正常昵称" \
'curl -X POST "$API_BASE_URL/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d "{\"nickname\": \"小明同学\"}" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试2：昵称上传接口 - 可能违规的昵称
test_api "昵称上传 - 可能违规的昵称" \
'curl -X POST "$API_BASE_URL/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d "{\"nickname\": \"习近平\"}" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试2.1：昵称上传接口 - 可能违规的昵称（习大大）
test_api "昵称上传 - 可能违规的昵称（习大大）" \
'curl -X POST "$API_BASE_URL/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d "{\"nickname\": \"习大大\"}" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试3：昵称上传接口 - 空昵称
test_api "昵称上传 - 空昵称（应该失败）" \
'curl -X POST "$API_BASE_URL/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -d "{\"nickname\": \"\"}" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试4：头像上传接口 - 正常图片
if [ -f "$TEST_AVATAR_PATH" ]; then
    test_api "头像上传 - 正常图片" \
    'curl -X POST "$API_BASE_URL/api/v1/upload_avatar" \
      -H "client-id: $CLIENT_ID" \
      -H "device-id: $DEVICE_ID" \
      -H "authorization: Bearer $AUTH_TOKEN" \
      -F "avatar=@$TEST_AVATAR_PATH" \
      -w "\\n状态码: %{http_code}\\n" \
      -v'
else
    echo "测试: 头像上传 - 正常图片"
    echo "----------------------------------------"
    echo "❌ 跳过测试 - 测试图片文件不存在: $TEST_AVATAR_PATH"
    echo ""
    echo "========================================"
    echo ""
fi

# 测试5：头像上传接口 - 没有文件
test_api "头像上传 - 没有文件（应该失败）" \
'curl -X POST "$API_BASE_URL/api/v1/upload_avatar" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -F "invalid_field=test" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试6：头像上传接口 - 错误的文件类型
echo "这是一个文本文件，不是图片" > /tmp/test_invalid.txt
test_api "头像上传 - 错误的文件类型（应该失败）" \
'curl -X POST "$API_BASE_URL/api/v1/upload_avatar" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer $AUTH_TOKEN" \
  -F "avatar=@/tmp/test_invalid.txt" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'
rm -f /tmp/test_invalid.txt

# 测试7：认证失败示例 - 昵称上传
test_api "认证失败 - 昵称上传（使用错误token）" \
'curl -X POST "$API_BASE_URL/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: $CLIENT_ID" \
  -H "device-id: $DEVICE_ID" \
  -H "authorization: Bearer invalid-token" \
  -d "{\"nickname\": \"测试昵称\"}" \
  -w "\\n状态码: %{http_code}\\n" \
  -v'

# 测试8：认证失败示例 - 头像上传
if [ -f "$TEST_AVATAR_PATH" ]; then
    test_api "认证失败 - 头像上传（使用错误token）" \
    'curl -X POST "$API_BASE_URL/api/v1/upload_avatar" \
      -H "client-id: $CLIENT_ID" \
      -H "device-id: $DEVICE_ID" \
      -H "authorization: Bearer invalid-token" \
      -F "avatar=@$TEST_AVATAR_PATH" \
      -w "\\n状态码: %{http_code}\\n" \
      -v'
else
    echo "测试: 认证失败 - 头像上传"
    echo "----------------------------------------"
    echo "❌ 跳过测试 - 测试图片文件不存在: $TEST_AVATAR_PATH"
    echo ""
    echo "========================================"
    echo ""
fi

echo "上传接口测试完成！"
echo ""
echo "📋 测试结果说明："
echo "- 200: 成功"
echo "- 400: 请求错误（如违规内容、文件格式错误等）"
echo "- 401: 认证失败"
echo "- 500: 服务器错误"
echo ""
echo "🔧 如果需要查看详细的服务器日志："
echo "tail -f tmp/server.log"
echo ""
echo "💡 如果JSON格式化失败，可能是响应包含非JSON内容"
echo "但这不影响API功能的正常工作"
echo "========================================"
