# YuYan HTTP API 测试文档

本目录包含 YuYan HTTP API 接口的测试脚本和使用文档，特别是新增的上传接口功能。

## 文件结构

```
test/http_api/
├── README.md           # 本文档
├── http_api_test.sh    # 原有API接口测试脚本
└── upload_test.sh      # 上传接口测试脚本（包含完整测试用例）
```

## 新增上传接口

### 1. 头像上传接口

**接口地址**: `POST /api/v1/upload_avatar`

**功能描述**: 上传用户头像图片，自动进行CDN存储和内容安全检测（仅校验，不保存到Redis）

#### 请求格式

**Headers**:
```
Content-Type: multipart/form-data
client-id: {客户端ID}
device-id: {设备ID}
authorization: Bearer {认证Token}
```

**Form Data**:
```
avatar: {图片文件}
```

#### 文件要求

- **支持格式**: `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.tiff`, `.tif`, `.heif`
- **文件大小**: 最大 5MB
- **字段名称**: `avatar`

#### 响应格式

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "头像上传成功",
  "data": {
    "avatar_url": "https://image.supamob.com.cn/imageView2/cloud/f4a73d4e/avatars/client123/1234567890_avatar.jpg",
    "is_blocked": false,
    "detect_result": "PASS",
    "upload_info": {
      "filename": "avatar.jpg",
      "file_size": 123456
    }
  },
  "timestamp": "2025-06-24T16:46:19.406290"
}
```

**违规响应 (400)**:
```json
{
  "success": false,
  "message": "头像内容违规，请更换符合规范的头像",
  "data": {
    "avatar_url": "https://image.supamob.com.cn/imageView2/cloud/f4a73d4e/avatars/...",
    "is_blocked": true,
    "detect_result": "BLOCK",
    "label_code": "Porn",
    "violation_reason": "包含色情内容",
    "upload_info": {
      "filename": "avatar.jpg",
      "file_size": 123456
    }
  },
  "timestamp": "2025-06-24T16:46:19.406290"
}
```

#### 使用示例

```bash
# 使用curl上传头像
curl -X POST "http://**************:8100/api/v1/upload_avatar" \
  -H "client-id: 27u5ct86k111001mb55p2rw1lbqm0364" \
  -H "device-id: 7f:83:ab:82:93:f9" \
  -H "authorization: Bearer your-token1" \
  -F "avatar=@/path/to/avatar.jpg"
```

### 2. 昵称上传接口

**接口地址**: `POST /api/v1/upload_nickname`

**功能描述**: 上传用户昵称，自动进行内容安全检测（仅校验，不保存到Redis）

#### 请求格式

**Headers**:
```
Content-Type: application/json
client-id: {客户端ID}
device-id: {设备ID}
authorization: Bearer {认证Token}
```

**Request Body**:
```json
{
  "nickname": "用户昵称"
}
```

#### 响应格式

**成功响应 (200)**:
```json
{
  "success": true,
  "message": "昵称上传成功",
  "data": {
    "nickname": "用户昵称",
    "is_blocked": false,
    "detect_result": "PASS"
  },
  "timestamp": "2025-06-24T16:32:13.779159"
}
```

**违规响应 (400)**:
```json
{
  "success": false,
  "message": "昵称内容违规，请更换符合规范的昵称",
  "data": {
    "nickname": "违规昵称",
    "is_blocked": true,
    "detect_result": "BLOCK",
    "label_code": "Illegal",
    "rewrite_content": "建议的替换内容",
    "violation_reason": "包含违法犯罪内容"
  },
  "timestamp": "2025-06-24T16:32:13.779159"
}
```

#### 使用示例

```bash
# 使用curl上传昵称
curl -X POST "http://**************:8100/api/v1/upload_nickname" \
  -H "Content-Type: application/json" \
  -H "client-id: 27u5ct86k111001mb55p2rw1lbqm0364" \
  -H "device-id: 7f:83:ab:82:93:f9" \
  -H "authorization: Bearer your-token1" \
  -d '{"nickname": "我的昵称"}'
```

## 认证要求

所有上传接口都需要以下认证信息：

### 必需Headers

- **client-id**: 客户端标识符
  - 格式: 32位随机字符串
  - 示例: `27u5ct86k111001mb55p2rw1lbqm0364`

- **device-id**: 设备标识符
  - 格式: MAC地址格式
  - 示例: `7f:83:ab:82:93:f9`

- **authorization**: 认证令牌
  - 格式: `Bearer {token}`
  - 示例: `Bearer your-token1`

## CDN上传服务

头像上传功能需要CDN服务支持：

### 功能特点

- 自动上传到CDN并获取可访问URL
- 支持多种图片格式
- 文件大小和格式验证
- 错误处理和重试机制

### CDN响应结构

```json
{
  "success": true,
  "data": {
    "code": 0,
    "requestId": "b28ab1b0-50d7-11f0-8350-dd90d16fd0eb",
    "data": {
      "url": "cloud/f4a73d4e/avatars/client123/1750754778_test01.jpeg",
      "mainColor": "#40383B",
      "host": "https://image.supamob.com.cn",
      "publicURL": "https://image.supamob.com.cn/imageView2/cloud/f4a73d4e/avatars/client123/1750754778_test01.jpeg",
      "size": 6577
    }
  },
  "url": "",
  "path": "avatars/client123/1750754778_test01.jpeg"
}
```

## 测试脚本使用

### 快速测试

```bash
# 运行上传接口测试（包含8个完整测试用例）
./test/http_api/upload_test.sh

# 运行原有接口测试
./test/http_api/http_api_test.sh
```

### 配置测试环境

1. **修改API地址**:
   ```bash
   # 在upload_test.sh脚本中修改
   API_BASE_URL="http://your-api-server:8100"
   ```

2. **配置认证信息**:
   ```bash
   CLIENT_ID="your-client-id"
   DEVICE_ID="your-device-id"
   AUTH_TOKEN="your-token"
   ```

3. **准备测试图片**:
   ```bash
   # 修改测试图片路径
   TEST_AVATAR_PATH="../../core/unit_test/policy_check/avatars/test01.jpeg"
   ```

### 测试用例覆盖

**昵称上传测试**:
- ✅ 正常昵称上传
- ✅ 违规昵称检测（习近平）
- ✅ 空昵称验证
- ✅ 认证失败处理

**头像上传测试**:
- ✅ 正常图片上传
- ✅ 文件格式验证
- ✅ 缺少文件处理
- ✅ 认证失败处理

### 测试脚本特点

- **自动化测试函数**: `test_api()` 函数统一处理所有测试
- **JSON格式化**: 自动提取和格式化JSON响应
- **错误处理**: 完整的错误场景测试
- **状态码显示**: 显示HTTP状态码便于调试

## 错误代码说明

| 状态码 | 含义 | 常见原因 |
|--------|------|----------|
| 200 | 成功 | 请求正常处理 |
| 400 | 请求错误 | 文件格式错误、内容违规、参数缺失 |
| 401 | 认证失败 | Token无效、Headers缺失 |
| 413 | 文件过大 | 上传文件超过5MB限制 |
| 500 | 服务器错误 | CDN上传失败、内部处理错误 |

## 接口特性

### 重要更新

1. **仅校验模式**: 
   - 上传接口只进行内容安全检测
   - 不会保存数据到Redis
   - 适用于预检测场景

2. **简化响应结构**:
   - 移除了 `remote_path` 字段
   - 保留核心信息：`avatar_url`、`filename`、`file_size`

3. **增强CDN集成**:
   - 支持嵌套CDN响应结构
   - 自动提取 `publicURL` 字段
   - 完整的错误日志记录

## 常见问题

### Q1: 头像上传失败，显示CDN错误
**A**: 检查CDN服务配置，确保网络连接正常，查看服务器日志获取详细错误信息。

### Q2: 昵称被误判为违规
**A**: 内容检测基于AI模型，可能存在误判。可以查看返回的`rewrite_content`字段获取建议修改内容。

### Q3: 认证一直失败
**A**: 检查以下配置：
- `config.yaml`中的认证配置是否正确
- Headers格式是否正确（注意Bearer前缀）
- client-id和device-id格式是否符合要求

### Q4: 支持哪些图片格式？
**A**: 支持 jpg, jpeg, png, webp, gif, tiff, tif, heif 格式，文件大小不超过5MB。

### Q5: 为什么接口不保存数据？
**A**: 当前版本接口设计为仅校验模式，用于预检测内容是否合规。实际保存操作需要在其他业务流程中处理。

## 开发集成

### JavaScript/TypeScript 示例

```javascript
// 头像上传
async function uploadAvatar(file, clientId, deviceId, token) {
  const formData = new FormData();
  formData.append('avatar', file);
  
  const response = await fetch('/api/v1/upload_avatar', {
    method: 'POST',
    headers: {
      'client-id': clientId,
      'device-id': deviceId,
      'authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  return await response.json();
}

// 昵称上传
async function uploadNickname(nickname, clientId, deviceId, token) {
  const response = await fetch('/api/v1/upload_nickname', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'client-id': clientId,
      'device-id': deviceId,
      'authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ nickname })
  });
  
  return await response.json();
}
```

### Python 示例

```python
import requests

def upload_avatar(file_path, client_id, device_id, token):
    """上传头像"""
    url = "http://**************:8100/api/v1/upload_avatar"
    headers = {
        'client-id': client_id,
        'device-id': device_id,
        'authorization': f'Bearer {token}'
    }
    
    with open(file_path, 'rb') as f:
        files = {'avatar': f}
        response = requests.post(url, headers=headers, files=files)
    
    return response.json()

def upload_nickname(nickname, client_id, device_id, token):
    """上传昵称"""
    url = "http://**************:8100/api/v1/upload_nickname"
    headers = {
        'Content-Type': 'application/json',
        'client-id': client_id,
        'device-id': device_id,
        'authorization': f'Bearer {token}'
    }
    
    data = {'nickname': nickname}
    response = requests.post(url, headers=headers, json=data)
    
    return response.json()
```