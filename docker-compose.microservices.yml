# YuYan服务微服务化Docker Compose配置
version: '3.8'

services:
  # WebSocket服务 - 处理实时语音对话
  websocket-server:
    build:
      context: .
      dockerfile: Dockerfile.websocket
    container_name: yuyan-websocket
    ports:
      - "8000:8000"   # WebSocket端口
      - "8080:8080"   # OTA端口
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
      - ./plugins_func:/app/plugins_func
      - ./models:/app/models
    environment:
      - SERVICE_NAME=websocket
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TZ=Asia/Shanghai
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s=socket.socket(); s.connect(('localhost', 8000)); s.close()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # HTTP API服务 - 处理RESTful API请求
  http-api-server:
    build:
      context: .
      dockerfile: Dockerfile.http
    container_name: yuyan-http-api
    ports:
      - "8100:8100"   # HTTP API端口
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
      - ./plugins_func:/app/plugins_func
    environment:
      - SERVICE_NAME=http-api
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TZ=Asia/Shanghai
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Redis服务 - 用户数据和会话存储
  redis:
    image: redis:7.2-alpine
    container_name: yuyan-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - yuyan-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Nginx反向代理 - 统一入口和负载均衡
  nginx:
    image: nginx:1.25-alpine
    container_name: yuyan-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - websocket-server
      - http-api-server
    restart: unless-stopped
    networks:
      - yuyan-network

volumes:
  redis_data:
    driver: local

networks:
  yuyan-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16