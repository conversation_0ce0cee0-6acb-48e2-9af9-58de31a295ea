{"version": "0.2.0", "configurations": [{"name": "🚀 单体服务 (原始app.py)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/", "python": "${workspaceFolder}/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/.venv/lib/python3.12/site-packages", "PATH": "/opt/homebrew/opt/ffmpeg@6/bin:${env:PATH}", "DYLD_LIBRARY_PATH": "/opt/homebrew/opt/ffmpeg@6/lib:${env:DYLD_LIBRARY_PATH}"}, "justMyCode": true}, {"name": "🔧 多进程管理器 (start_services.py)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/alternatives/start_services.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/", "python": "${workspaceFolder}/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/.venv/lib/python3.12/site-packages", "PATH": "/opt/homebrew/opt/ffmpeg@6/bin:${env:PATH}", "DYLD_LIBRARY_PATH": "/opt/homebrew/opt/ffmpeg@6/lib:${env:DYLD_LIBRARY_PATH}"}, "justMyCode": true}, {"name": "🌐 WebSocket服务 (独立调试)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/alternatives/app_ws.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/", "python": "${workspaceFolder}/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/.venv/lib/python3.12/site-packages", "PATH": "/opt/homebrew/opt/ffmpeg@6/bin:${env:PATH}", "DYLD_LIBRARY_PATH": "/opt/homebrew/opt/ffmpeg@6/lib:${env:DYLD_LIBRARY_PATH}", "SERVICE_NAME": "websocket", "REDIS_HOST": "localhost", "REDIS_PORT": "6379"}, "justMyCode": true}, {"name": "🔗 HTTP API服务 (独立调试)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/alternatives/app_http.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/", "python": "${workspaceFolder}/.venv/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}:${workspaceFolder}/.venv/lib/python3.12/site-packages", "PATH": "/opt/homebrew/opt/ffmpeg@6/bin:${env:PATH}", "DYLD_LIBRARY_PATH": "/opt/homebrew/opt/ffmpeg@6/lib:${env:DYLD_LIBRARY_PATH}", "SERVICE_NAME": "http-api", "REDIS_HOST": "localhost", "REDIS_PORT": "6379"}, "justMyCode": true}], "compounds": [{"name": "🏃‍♂️ 同时调试两个服务", "configurations": ["🌐 WebSocket服务 (独立调试)", "🔗 HTTP API服务 (独立调试)"], "stopAll": true, "presentation": {"hidden": false, "group": "microservices", "order": 1}}]}