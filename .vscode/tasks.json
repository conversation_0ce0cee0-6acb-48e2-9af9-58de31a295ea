{"version": "2.0.0", "tasks": [{"label": "🐳 启动Docker微服务", "type": "shell", "command": "./docker-start.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "使用Docker Compose启动所有微服务"}, {"label": "🛑 停止Docker微服务", "type": "shell", "command": "./docker-manage.sh", "args": ["stop"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "停止所有Docker微服务"}, {"label": "📊 查看服务状态", "type": "shell", "command": "./docker-manage.sh", "args": ["status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "查看Docker微服务运行状态"}, {"label": "🔍 查看服务日志", "type": "shell", "command": "./docker-manage.sh", "args": ["logs"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "查看Docker微服务日志"}, {"label": "❤️ 健康检查", "type": "shell", "command": "./docker-manage.sh", "args": ["health"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "检查所有微服务健康状态"}, {"label": "🔨 构建Docker镜像", "type": "shell", "command": "docker-compose", "args": ["-f", "docker-compose.microservices.yml", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "构建所有Docker微服务镜像"}, {"label": "🧹 清理Docker资源", "type": "shell", "command": "./docker-manage.sh", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "清理停止的容器和未使用的镜像"}, {"label": "🖥️ 实时监控", "type": "shell", "command": "./docker-manage.sh", "args": ["monitor"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "detail": "实时监控所有微服务状态和资源使用"}]}