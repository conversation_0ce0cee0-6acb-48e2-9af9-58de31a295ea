#!/bin/bash
# YuYan微服务Docker管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

COMPOSE_FILE="docker-compose.microservices.yml"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}YuYan微服务Docker管理脚本${NC}"
    echo "使用方法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     - 启动所有服务"
    echo "  stop      - 停止所有服务"
    echo "  restart   - 重启所有服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看服务日志"
    echo "  health    - 检查服务健康状态"
    echo "  scale     - 扩缩容服务"
    echo "  update    - 更新服务镜像"
    echo "  clean     - 清理停止的容器和镜像"
    echo "  monitor   - 实时监控服务状态"
    echo ""
    echo "示例:"
    echo "  $0 start                     # 启动所有服务"
    echo "  $0 logs websocket-server     # 查看WebSocket服务日志"
    echo "  $0 scale http-api-server=3   # 扩容HTTP API服务到3个实例"
}

# 启动服务
start_services() {
    log_info "启动YuYan微服务..."
    docker-compose -f $COMPOSE_FILE up -d
    sleep 5
    check_health
}

# 停止服务
stop_services() {
    log_info "停止YuYan微服务..."
    docker-compose -f $COMPOSE_FILE down
    log_info "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启YuYan微服务..."
    docker-compose -f $COMPOSE_FILE restart
    sleep 5
    check_health
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    log_info "容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" $(docker-compose -f $COMPOSE_FILE ps -q)
}

# 查看日志
show_logs() {
    local service_name=$1
    if [ -z "$service_name" ]; then
        log_info "显示所有服务日志 (按Ctrl+C退出):"
        docker-compose -f $COMPOSE_FILE logs -f
    else
        log_info "显示 $service_name 服务日志 (按Ctrl+C退出):"
        docker-compose -f $COMPOSE_FILE logs -f $service_name
    fi
}

# 健康检查
check_health() {
    log_info "检查服务健康状态..."
    
    local all_healthy=true
    
    # 检查Redis
    if docker-compose -f $COMPOSE_FILE exec -T redis redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} Redis服务健康"
    else
        echo -e "${RED}✗${NC} Redis服务不健康"
        all_healthy=false
    fi
    
    # 检查HTTP API服务
    if curl -f http://localhost:8100/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} HTTP API服务健康"
    else
        echo -e "${RED}✗${NC} HTTP API服务不健康"
        all_healthy=false
    fi
    
    # 检查WebSocket服务
    if nc -z localhost 8000 > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} WebSocket服务健康"
    else
        echo -e "${RED}✗${NC} WebSocket服务不健康"
        all_healthy=false
    fi
    
    # 检查Nginx
    if curl -f http://localhost/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} Nginx服务健康"
    else
        echo -e "${RED}✗${NC} Nginx服务不健康"
        all_healthy=false
    fi
    
    if [ "$all_healthy" = true ]; then
        log_info "所有服务运行正常"
        show_endpoints
    else
        log_warn "部分服务不健康，建议检查日志"
    fi
}

# 显示服务端点
show_endpoints() {
    echo ""
    log_info "服务访问端点:"
    echo -e "  ${BLUE}WebSocket:${NC} ws://localhost:8000/xiaozhi/v1/"
    echo -e "  ${BLUE}HTTP API:${NC} http://localhost:8100/api/v1/"
    echo -e "  ${BLUE}OTA服务:${NC} http://localhost:8080/xiaozhi/ota/"
    echo -e "  ${BLUE}Nginx入口:${NC} http://localhost/"
}

# 扩缩容服务
scale_service() {
    local scale_config=$1
    if [ -z "$scale_config" ]; then
        log_error "请提供扩缩容配置，例如: websocket-server=2"
        return 1
    fi
    
    log_info "扩缩容服务: $scale_config"
    docker-compose -f $COMPOSE_FILE up -d --scale $scale_config
    show_status
}

# 更新服务镜像
update_services() {
    log_info "拉取最新镜像..."
    docker-compose -f $COMPOSE_FILE pull
    
    log_info "重建并重启服务..."
    docker-compose -f $COMPOSE_FILE up -d --build
    
    log_info "清理旧镜像..."
    docker image prune -f
    
    check_health
}

# 清理资源
clean_resources() {
    log_info "清理停止的容器..."
    docker container prune -f
    
    log_info "清理未使用的镜像..."
    docker image prune -f
    
    log_info "清理未使用的网络..."
    docker network prune -f
    
    log_info "清理未使用的卷..."
    docker volume prune -f
    
    log_info "清理完成"
}

# 实时监控
monitor_services() {
    log_info "实时监控服务状态 (按Ctrl+C退出)..."
    echo ""
    
    while true; do
        clear
        echo -e "${BLUE}YuYan微服务监控面板${NC} - $(date)"
        echo "========================================"
        
        # 显示容器状态
        echo -e "\n${YELLOW}容器状态:${NC}"
        docker-compose -f $COMPOSE_FILE ps
        
        # 显示资源使用
        echo -e "\n${YELLOW}资源使用:${NC}"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker-compose -f $COMPOSE_FILE ps -q)
        
        # 显示健康状态
        echo -e "\n${YELLOW}健康检查:${NC}"
        check_health > /dev/null 2>&1
        
        sleep 5
    done
}

# 主函数
main() {
    local command=$1
    local arg=$2
    
    case $command in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs $arg
            ;;
        "health")
            check_health
            ;;
        "scale")
            scale_service $arg
            ;;
        "update")
            update_services
            ;;
        "clean")
            clean_resources
            ;;
        "monitor")
            monitor_services
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"